#  上下文
文件名：api_integration_task.md
创建于：[YYYY-MM-DD HH:MM:SS]
创建者：AI
关联协议：RIPER-5 + Multidimensional + Agent Protocol

# 任务描述
对接一下前后端，进行联调。同时生成一份md文档来记录接口的一致性，如有前后端不一致，请及时修改并记录

# 项目概述
根据用户需求，进行前后端接口的对接联调，确保数据一致性，并生成文档记录。如发现不一致，将进行修改并记录。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
- 前端项目路径：/C:/Users/<USER>/Desktop/Yilena-take-out/frontend
- 后端项目模块：
    - sky-common: /C:/Users/<USER>/Desktop/Yilena-take-out/sky-common
    - sky-pojo: /C:/Users/<USER>/Desktop/Yilena-take-out/sky-pojo
    - sky-server: /C:/Users/<USER>/Desktop/Yilena-take-out/sky-server
- API文档：用户表示Swagger文档尚未生成，但代码中已为所有接口添加了"主API注解"。
- 后端技术栈提示：`sky-pojo/pom.xml` 包含 `knife4j-spring-boot-starter`，暗示使用 Spring Boot 和 Knife4j。
- API注解：通过在 `sky-server/src` 中搜索，确认后端广泛使用 `@Api` (在Controller类上) 和 `@ApiOperation` (在方法上) 注解来定义接口。这些注解将作为提取API信息的来源。
- 前端API调用位置：`/C:/Users/<USER>/Desktop/Yilena-take-out/frontend/src/api` 目录，包含多个TypeScript文件，用于定义对不同模块的API请求。

[正在进行的分析...]

**后端 API 分析 (初步):**

**Controller: `EmployeeController.java` (Base Path: `/admin/employee`)**
 DTOs 均位于 `sky-pojo/src/main/java/com/sky/dto/`

1.  **员工登录**
    *   `@ApiOperation(value = "员工登录")`
    *   **HTTP Method:** `POST`
    *   **Path:** `/login`
    *   **Request Body:** `EmployeeLoginDTO`
        *   `username` (String)
        *   `password` (String)
    *   **Response:** `Result<EmployeeLoginVO>` (包含 `id`, `userName`, `name`, `token`)

2.  **员工退出**
    *   `@ApiOperation(value = "员工退出")`
    *   **HTTP Method:** `POST`
    *   **Path:** `/logout`
    *   **Request Body:** None
    *   **Response:** `Result<String>`

3.  **员工分页查询**
    *   `@ApiOperation(value = "员工分页查询")`
    *   **HTTP Method:** `GET`
    *   **Path:** `/page`
    *   **Request Parameters:** `EmployeePageQueryDTO`
        *   `name` (String) - 员工姓名
        *   `page` (int) - 页码
        *   `pageSize` (int) - 每页显示记录数
    *   **Response:** `Result<PageResult>`

4.  **新增员工**
    *   `@ApiOperation(value = "新增员工")`
    *   **HTTP Method:** `POST`
    *   **Path:** `/admin/employee` (根路径)
    *   **Request Body:** `EmployeeDTO`
        *   `id` (Long)
        *   `username` (String)
        *   `name` (String)
        *   `phone` (String)
        *   `sex` (String)
        *   `idNumber` (String) - 身份证号
    *   **Response:** `Result`

5.  **启用禁用员工账号**
    *   `@ApiOperation(value = "启用禁用员工账号")`
    *   **HTTP Method:** `POST`
    *   **Path:** `/status/{status}`
    *   **Path Variable:** `status` (Integer)
    *   **Query Parameter:** `id` (Long)
    *   **Response:** `Result`

6.  **编辑员工**
    *   `@ApiOperation(value = "编辑员工")`
    *   **HTTP Method:** `PUT`
    *   **Path:** `/admin/employee` (根路径)
    *   **Request Body:** `EmployeeDTO` (同上 "新增员工")
    *   **Response:** `Result`

7.  **根据ID查询员工**
    *   `@ApiOperation(value = "根据ID查询员工")`
    *   **HTTP Method:** `GET`
    *   **Path:** `/{id}`
    *   **Path Variable:** `id` (Long)
    *   **Response:** `Result<Employee>`

8.  **修改员工密码**
    *   `@ApiOperation(value = "修改员工密码")`
    *   **HTTP Method:** `PUT`
    *   **Path:** `/editPassword`
    *   **Request Body:** `PasswordEditDTO`
        *   `empId` (Long) - 员工id
        *   `oldPassword` (String) - 旧密码
        *   `newPassword` (String) - 新密码
    *   **Response:** `Result`

[后续将分析 EmployeeController 对应的前端API调用...]

**前端 API 分析 (`frontend/src/api/employee.ts`):**

1.  **登录 (`login`)**
    *   **Path:** `/admin/employee/login`
    *   **Method:** `post`
    *   **Data:** `data` (any type)
    *   **Backend Original Path:** `/admin/employee/login`

2.  **退出 (`userLogout`)**
    *   **Path:** `/admin/employee/logout`
    *   **Method:** `post`
    *   **Params:** `params` (any type)
    *   **Backend Original Path:** `/admin/employee/logout`

3.  **获取员工列表 (`getEmployeeList`)**
    *   **Path:** `/admin/employee/page`
    *   **Method:** `get`
    *   **Params:** `params` (any type)
    *   **Backend Original Path:** `/admin/employee/page`

4.  **启用禁用员工账号 (`enableOrDisableEmployee`)**
    *   **Path:** `/admin/employee/status/${params.status}`
    *   **Method:** `post`
    *   **Params:** `{ id: params.id }`
    *   **Response:** `Result`

5.  **新增员工 (`addEmployee`)**
    *   **Path:** `/admin/employee`
    *   **Method:** `post`
    *   **Data:** `{ ...params }`
    *   **Backend Original Path:** `/admin/employee`

6.  **修改员工 (`editEmployee`)**
    *   **Path:** `/admin/employee`
    *   **Method:** `put`
    *   **Data:** `{ ...params }`
    *   **Backend Original Path:** `/admin/employee`

7.  **根据ID查询员工 (`queryEmployeeById`)**
    *   **Path:** `/admin/employee/${id}`
    *   **Method:** `get`
    *   **Backend Original Path:** `/admin/employee/${id}`

**`EmployeeController` 与 `employee.ts` 接口初步不一致性总结:**

*   **路径前缀不一致:** 
    *   描述：所有前端API调用的路径都缺少了后端Controller类级别定义的 `/admin` 前缀。例如，前端调用 `/employee/login`，而后端实际是 `/admin/employee/login`。
    *   推测：这可能是通过前端 `request` 工具的基地址 (`baseURL`) 配置来统一添加 `/admin`，需要后续验证或修改。
    *   前端代码示例 (`employee.ts` - `login` function):
        ```typescript
        export const login = (data: any) =>
          request({
            'url': '/admin/employee/login', // Path used by frontend
            'method': 'post',
            data
          })
        ```
    *   后端代码示例 (`EmployeeController.java` - class and login method mapping):
        ```java
        @RequestMapping("/admin/employee") // Base path for controller
        // ...
        @PostMapping("/login") // Path for login method
        public Result<EmployeeLoginVO> login(@RequestBody EmployeeLoginDTO employeeLoginDTO) { /*...*/ }
        ```

*   **员工退出接口参数不一致 (`userLogout`):**
    *   描述：前端 `userLogout` 向 `/employee/logout` 路径的POST请求传递了 `params`，但后端对应的 `logout` 方法不接收任何参数。
    *   前端代码示例 (`employee.ts`):
        ```typescript
        export const userLogout = (params: any) =>
          request({
            'url': `/admin/employee/logout`,
            'method': 'post',
            params // Frontend sends params
          })
        ```
    *   后端代码示例 (`EmployeeController.java`):
        ```java
        @PostMapping("/logout")
        public Result<String> logout() { // Backend expects no parameters
            return Result.success();
        }
        ```

*   **缺失前端API调用:** 
    *   描述：后端 `EmployeeController` 中定义了 "修改员工密码" (`PUT /admin/employee/editPassword`) 的接口，但在 `frontend/src/api/employee.ts` 中未找到对应的API调用函数。
    *   后端代码示例 (`EmployeeController.java`):
        ```java
        @ApiOperation(value = "修改员工密码", notes = "修改员工登录密码")
        @PutMapping("/editPassword")
        public Result updatePassword(@RequestBody PasswordEditDTO passwordEditDTO) { /*...*/ }
        ```

[后续将分析其他Controller和对应的API调用...]

**Controller: `CategoryController.java` (Base Path: `/admin/category`)**
 DTOs 均位于 `sky-pojo/src/main/java/com/sky/dto/`

1.  **分类分页查询**
    *   `@ApiOperation(value = "分类分页查询")`
    *   **HTTP Method:** `GET`
    *   **Path:** `/page`
    *   **Request Parameters:** `CategoryPageQueryDTO`
        *   `page` (int)
        *   `pageSize` (int)
        *   `name` (String, optional)
        *   `type` (Integer, optional, 1菜品分类，2套餐分类)
    *   **Response:** `Result<PageResult>`

2.  **新增分类**
    *   `@ApiOperation(value = "新增分类")`
    *   **HTTP Method:** `POST`
    *   **Path:** `/admin/category` (根路径)
    *   **Request Body:** `CategoryDTO`
        *   `id` (Long, optional)
        *   `type` (Integer, required)
        *   `name` (String, required)
        *   `sort` (Integer, required)
    *   **Response:** `Result`

3.  **启用禁用分类**
    *   `@ApiOperation(value = "启用禁用分类")`
    *   **HTTP Method:** `POST`
    *   **Path:** `/status/{status}`
    *   **Path Variable:** `status` (Integer)
    *   **Query Parameter:** `id` (Long)
    *   **Response:** `Result`

4.  **编辑分类**
    *   `@ApiOperation(value = "编辑分类")`
    *   **HTTP Method:** `PUT`
    *   **Path:** `/admin/category` (根路径)
    *   **Request Body:** `CategoryDTO` (同上 "新增分类")
    *   **Response:** `Result`

5.  **删除分类**
    *   `@ApiOperation(value = "删除分类")`
    *   **HTTP Method:** `DELETE`
    *   **Path:** `/admin/category` (根路径)
    *   **Query Parameter:** `id` (Long)
    *   **Response:** `Result`

6.  **根据类型查询分类**
    *   `@ApiOperation(value = "根据类型查询分类")`
    *   **HTTP Method:** `GET`
    *   **Path:** `/list`
    *   **Query Parameter:** `type` (Integer, optional)
    *   **Response:** `Result<List<Category>>`

**前端 API 分析 (`frontend/src/api/category.ts`):**

1.  **查询分类列表 (`getCategoryPage`)**
    *   **Path:** `/admin/category/page`
    *   **Method:** `get`
    *   **Params:** `params`
    *   **Backend Original Path:** `/admin/category/page`

2.  **删除当前列 (`deleCategory`)**
    *   **Path:** `/admin/category`
    *   **Method:** `delete`
    *   **Params:** `{ id: ids }`
    *   **Backend Original Path:** `/admin/category`

3.  **修改接口 (`editCategory`)**
    *   **Path:** `/admin/category`
    *   **Method:** `put`
    *   **Data:** `{ ...params }`
    *   **Backend Original Path:** `/admin/category`

4.  **新增接口 (`addCategory`)**
    *   **Path:** `/admin/category`
    *   **Method:** `post`
    *   **Data:** `{ ...params }`
    *   **Backend Original Path:** `/admin/category`

5.  **修改---启用禁用接口 (`enableOrDisableEmployee`)**
    *   **Path:** `/admin/category/status/${params.status}`
    *   **Method:** `post`
    *   **Params:** `{ id: params.id }`
    *   **Backend Original Path:** `/admin/category/status/{status}`

**`CategoryController` 与 `category.ts` 接口初步不一致性总结:**

*   **路径前缀不一致:** 
    *   描述：与 `EmployeeController` 情况类似，所有前端API调用的路径都缺少了后端Controller类级别定义的 `/admin` 前缀。
    *   前端代码示例 (`category.ts` - `getCategoryPage` function):
        ```typescript
        export const getCategoryPage = (params: any) => {
          return request({
            url: '/admin/category/page', // Path used by frontend
            method: 'get',
            params
          });
        };
        ```
    *   后端代码示例 (`CategoryController.java` - class mapping):
        ```java
        @RequestMapping("/admin/category") // Base path for controller
        // ...
        ```

*   **删除接口参数名差异 (`deleCategory`):**
    *   描述：前端使用 `ids` 作为输入参数，然后构造 `{ id: ids }` 作为请求参数；后端直接期望名为 `id` 的查询参数。
    *   前端代码示例 (`category.ts`):
        ```typescript
        export const deleCategory = (ids: string) => {
          return request({
            url: '/admin/category',
            method: 'delete',
            params: { id:ids } // param is { id: string_value }
          });
        };
        ```
    *   后端代码示例 (`CategoryController.java`):
        ```java
        @DeleteMapping
        public Result deleteCategory(Long id) { // expects Long id directly from query param "id"
            categoryService.deleteCategory(id);
            return Result.success();
        }
        ```

*   **启用禁用接口函数命名不当:**
    *   描述：前端 `category.ts` 中的函数 `enableOrDisableEmployee` 用于分类的启用禁用，命名明显错误，应更改为如 `enableOrDisableCategory`。
    *   前端代码示例 (`category.ts`):
        ```typescript
        // 修改---启用禁用接口
        export const enableOrDisableEmployee = (params: any) => { // Incorrect function name
          return request({
            url: `/admin/category/status/${params.status}`,
            method: 'post',
            params: { id:params.id }
          })
        }
        ```

*   **缺失前端API调用:** 
    *   描述：后端 `CategoryController` 中定义了 "根据类型查询分类" (`GET /admin/category/list`) 接口，但在 `frontend/src/api/category.ts` 中未找到对应的API调用函数。
    *   后端代码示例 (`CategoryController.java`):
        ```java
        @ApiOperation(value = "根据类型查询分类", ...)
        @GetMapping("/list")
        public Result<List<Category>> findByType(Integer type) { /*...*/ }
        ```

[后续将分析其他Controller和对应的API调用...]

**Controller: `DishController.java` (Base Path: `/admin/dish`)**
 DTOs/VOs 均位于 `sky-pojo/src/main/java/com/sky/{dto,vo}/`

1.  **菜品分页查询**
    *   `@ApiOperation(value = "菜品分页查询")`
    *   **HTTP Method:** `GET`
    *   **Path:** `/page`
    *   **Request Parameters:** `DishPageQueryDTO`
        *   `page` (int), `pageSize` (int), `name` (String, optional), `categoryId` (Integer, optional), `status` (Integer, optional)
    *   **Response:** `Result<PageResult>`

2.  **新增菜品**
    *   `@ApiOperation(value = "新增菜品")`
    *   **HTTP Method:** `POST`
    *   **Path:** `/admin/dish` (根路径)
    *   **Request Body:** `DishDTO`
        *   `id` (Long, opt), `name` (String, req), `categoryId` (Long, req), `price` (BigDecimal, req), `image` (String, opt), `description` (String, opt), `status` (Integer, req), `flavors` (List<DishFlavor>)
    *   **Response:** `Result`

3.  **批量删除菜品**
    *   `@ApiOperation(value = "批量删除菜品")`
    *   **HTTP Method:** `DELETE`
    *   **Path:** `/admin/dish` (根路径)
    *   **Query Parameter:** `ids` (List<Long>)
    *   **Response:** `Result`

4.  **根据ID查询菜品**
    *   `@ApiOperation(value = "根据ID查询菜品")`
    *   **HTTP Method:** `GET`
    *   **Path:** `/{id}`
    *   **Path Variable:** `id` (Long)
    *   **Response:** `Result<DishVO>` (includes `flavors`, `categoryName`)

5.  **编辑菜品**
    *   `@ApiOperation(value = "编辑菜品")`
    *   **HTTP Method:** `PUT`
    *   **Path:** `/admin/dish` (根路径)
    *   **Request Body:** `DishDTO` (same as 新增菜品)
    *   **Response:** `Result`

6.  **菜品起售停售**
    *   `@ApiOperation(value = "菜品起售停售")`
    *   **HTTP Method:** `POST`
    *   **Path:** `/status/{status}`
    *   **Path Variable:** `status` (Integer), **Query Parameter:** `id` (Long)
    *   **Response:** `Result`

7.  **根据分类ID查询菜品**
    *   `@ApiOperation(value = "根据分类ID查询菜品")`
    *   **HTTP Method:** `GET`
    *   **Path:** `/list`
    *   **Query Parameter:** `categoryId` (Long)
    *   **Response:** `Result<List<DishVO>>`

**前端 API 分析 (`frontend/src/api/dish.ts`):**

1.  **查询列表接口 (`getDishPage`)**: Path: `/admin/dish/page`, Method: `get`, Params: `params`. (Backend: `/admin/dish/page`)
2.  **删除接口 (`deleteDish`)**: Path: `/admin/dish`, Method: `delete`, Params: `{ ids }` (ids: string). (Backend: `/admin/dish`, expects `List<Long>` for query param `ids`)
3.  **修改接口 (`editDish`)**: Path: `/admin/dish`, Method: `put`, Data: `{ ...params }`. (Backend: `/admin/dish`)
4.  **新增接口 (`addDish`)**: Path: `/admin/dish`, Method: `post`, Data: `{ ...params }`. (Backend: `/admin/dish`)
5.  **查询详情 (`queryDishById`)**: Path: `/admin/dish/${id}`, Method: `get`. (Backend: `/admin/dish/${id}`)
6.  **获取菜品分类列表 (`getCategoryList`)**: Path: `/admin/category/list`, Method: `get`, Params: `{ ...params }`. (Belongs to Category, Backend: `/admin/category/list`)
7.  **查菜品列表的接口 (`queryDishList`)**: Path: `/admin/dish/list`, Method: `get`, Params: `params`. (Backend: `/admin/dish/list`)
8.  **文件down预览 (`commonDownload`)**: Path: `/admin/common/download`, Method: `get`. (Belongs to Common utils)
9.  **起售停售 (`dishStatusByStatus`)**: Path: `/admin/dish/status/${params.status}`, Method: `post`, Params: `{ id: params.id }`. (Backend: `/admin/dish/status/{status}`)
10. **菜品分类数据查询 (`dishCategoryList`)**: Path: `/admin/category/list`, Method: `get`, Params: `{ ...params }`. (Belongs to Category, Backend: `/admin/category/list`, redundant with `getCategoryList`)

**`DishController` 与 `dish.ts` 接口初步不一致性总结:**

*   **路径前缀不一致:** 
    
*   描述：普遍存在，前端API调用的路径缺少后端Controller类级别定义的 `/admin` 前缀。
    
*   **批量删除参数类型不匹配 (`deleteDish`):**
    *   描述：后端 `DishController` 的 `deleteDish` 方法期望 `@RequestParam("ids") List<Long> ids`。前端 `deleteDish` 函数发送 `params: { ids }` 其中 `ids` 是一个字符串 (e.g., "1,2,3"). Spring MVC 可能无法自动将此字符串转换为 `List<Long>`。如果不能，这将导致请求失败或行为不符合预期。
    *   前端代码示例 (`dish.ts`):
        ```typescript
        export const deleteDish = (ids: string) => {
          return request({
            url: '/admin/dish',
            method: 'delete',
            params: { ids } // ids is a string, e.g., "1,2,3"
          })
        }
        ```
    *   后端代码示例 (`DishController.java`):
        ```java
        @DeleteMapping
        public Result deleteDish(@RequestParam("ids") List<Long> ids) { // Expects List<Long>
            dishService.deleteDish(ids);
            return Result.success();
        }
        ```

*   **API 调用位置不当/冗余:**
    *   描述：`dish.ts` 中包含对分类接口 (`/category/list`) 的调用 (`getCategoryList`, `dishCategoryList`) 以及一个通用下载接口 (`/common/download`)。这些调用应分别归属到 `category.ts` (或其等价文件) 和通用API模块。
    *   `getCategoryList` 和 `dishCategoryList` 功能基本一致，后者显得冗余。
    *   前端代码示例 (`dish.ts`):
        ```typescript
        // 获取菜品分类列表
        export const getCategoryList = (params: any) => {
          return request({
            url: '/admin/category/list', // Calls category API
            method: 'get',
            params
          })
        }
        // ...
        //菜品分类数据查询
        export const dishCategoryList = (params: any) => {
          return request({
            url: `/admin/category/list`, // Calls category API again
            method: 'get',
            params: { ...params }
          })
        }
        ```

[后续将分析其他Controller和对应的API调用...]

**Controller: `SetMealController.java` (Base Path: `/admin/setmeal`)**
 DTOs/VOs 均位于 `sky-pojo/src/main/java/com/sky/{dto,vo}/`

1.  **套餐分页查询**
    *   `@ApiOperation(value = "套餐分页查询")`
    *   **HTTP Method:** `GET` (implied by `@RequestMapping("/page")`)
    *   **Path:** `/page`
    *   **Request Parameters:** `SetmealPageQueryDTO`
        *   `page` (int), `pageSize` (int), `name` (String, opt), `categoryId` (Integer, opt), `status` (Integer, opt)
    *   **Response:** `Result<PageResult>`

2.  **新增套餐**
    *   `@ApiOperation(value = "新增套餐")`
    *   **HTTP Method:** `POST`
    *   **Path:** `/admin/setmeal` (根路径)
    *   **Request Body:** `SetmealDTO`
        *   `id` (Long, opt), `categoryId` (Long, req), `name` (String, req), `price` (BigDecimal, req), `status` (Integer, req), `description` (String, opt), `image` (String, opt), `setmealDishes` (List<SetmealDish>)
    *   **Response:** `Result`

3.  **编辑套餐**
    *   `@ApiOperation(value = "编辑套餐")`
    *   **HTTP Method:** `PUT`
    *   **Path:** `/admin/setmeal` (根路径)
    *   **Request Body:** `SetmealDTO` (same as 新增套餐)
    *   **Response:** `Result`

4.  **根据ID查询套餐**
    *   `@ApiOperation(value = "根据ID查询套餐")`
    *   **HTTP Method:** `GET`
    *   **Path:** `/{id}`
    *   **Path Variable:** `id` (Long)
    *   **Response:** `Result<SetmealVO>` (includes `setmealDishes`, `categoryName`)

5.  **套餐起售停售**
    *   `@ApiOperation(value = "套餐起售停售")`
    *   **HTTP Method:** `POST`
    *   **Path:** `/status/{status}`
    *   **Path Variable:** `status` (Integer), **Query Parameter:** `id` (Long)
    *   **Response:** `Result`

6.  **批量删除套餐**
    *   `@ApiOperation(value = "批量删除套餐")`
    *   **HTTP Method:** `DELETE`
    *   **Path:** `/admin/setmeal` (根路径)
    *   **Query Parameter:** `ids` (List<Long>)
    *   **Response:** `Result`

**前端 API 分析 (`frontend/src/api/setMeal.ts`):**

1.  **查询列表数据 (`getSetmealPage`)**: Path: `/admin/setmeal/page`, Method: `get`. (Backend: `/admin/setmeal/page`)
2.  **删除数据接口 (`deleteSetmeal`)**: Path: `/admin/setmeal`, Method: `delete`, Params: `{ ids }` (ids: string). (Backend: `/admin/setmeal`, expects `List<Long>` for query param `ids`)
3.  **修改数据接口 (`editSetmeal`)**: Path: `/admin/setmeal`, Method: `put`, Data: `{ ...params }`. (Backend: `/admin/setmeal`)
4.  **新增数据接口 (`addSetmeal`)**: Path: `/admin/setmeal`, Method: `post`, Data: `{ ...params }`. (Backend: `/admin/setmeal`)
5.  **查询详情接口 (`querySetmealById`)**: Path: `/admin/setmeal/${id}`, Method: `get`. (Backend: `/admin/setmeal/${id}`)
6.  **批量起售禁售 (`setmealStatusByStatus`)**: Path: `/admin/setmeal/status/${params.status}`, Method: `post`, Params: `{ id: params.ids }`. (Backend: `/admin/setmeal/status/{status}`, expects query param `id` (Long), frontend sends `params.ids` for `id`)
7.  **菜品分类数据查询 (`dishCategoryList`)**: Path: `/admin/category/list`, Method: `get`. (Belongs to Category, Backend: `/admin/category/list`)

**`SetMealController` 与 `setMeal.ts` 接口初步不一致性总结:**

*   **路径前缀不一致:** 
    
*   描述：普遍存在，前端API调用的路径缺少后端Controller类级别定义的 `/admin` 前缀。
    
*   **批量删除参数类型不匹配 (`deleteSetmeal`):**
    *   描述：后端 `SetMealController` 的 `deleteSetMeal` 方法期望 `@RequestParam("ids") List<Long> ids`。前端 `deleteSetmeal` 函数发送 `params: { ids }` 其中 `ids` 是一个字符串。这与 `DishController` 的情况类似，可能导致参数绑定失败。
    *   前端代码示例 (`setMeal.ts`):
        ```typescript
        export const deleteSetmeal = (ids: string) => {
          return request({
            url: '/admin/setmeal',
            method: 'delete',
            params: { ids } // ids is a string
          })
        }
        ```
    *   后端代码示例 (`SetMealController.java`):
        ```java
        @DeleteMapping
        public Result deleteSetMeal(@RequestParam("ids") List<Long> ids) { // Expects List<Long>
            setMealService.deleteSetMeal(ids);
            return Result.success();
        }
        ```

*   **起售禁售参数不一致/命名不当 (`setmealStatusByStatus`):**
    *   描述：前端 `setmealStatusByStatus` 发送 `params: { id: params.ids }`。后端对应方法期望路径参数 `status` 和查询参数 `id` (Long)。如果 `params.ids` 是单个值，则前端将 `ids` 属性的值赋给了请求参数 `id`；如果 `params.ids` 是集合或逗号分隔字符串，则与后端期望的单个 `Long id` 不符。此外，后端参数名是 `id`，而前端原始变量是 `params.ids`。
    *   前端代码示例 (`setMeal.ts`):
        ```typescript
        export const setmealStatusByStatus = (params: any) => {
          return request({
            url: `/admin/setmeal/status/${params.status}`,
            method: 'post',
            params: { id: params.ids } // Sends params.ids as 'id' query parameter
          })
        }
        ```
    *   后端代码示例 (`SetMealController.java`):
        ```java
        @PostMapping("/status/{status}")
        public Result updateStatus(@PathVariable Integer status, Long id) { // Expects query param 'id' (Long)
            setMealService.updateStatus(status, id);
            return Result.success();
        }
        ```

*   **API 调用位置不当:**
    
    *   描述：`setMeal.ts` 中的 `dishCategoryList` 函数调用了 `/category/list` 接口，该接口属于分类管理，应在 `category.ts` 中定义和调用。

[后续将分析其他Controller和对应的API调用...]

**Controller: `OrderAdminController.java` (Base Path: `/admin/order`)**
 DTOs/VOs 均位于 `sky-pojo/src/main/java/com/sky/{dto,vo}/`

1.  **订单分页查询 (`findByPage`)**
    *   `@ApiOperation(value = "订单分页查询")`
    *   **HTTP Method:** `GET`, **Path:** `/conditionSearch`
    *   **Request Parameters:** `OrdersPageQueryDTO` (page, pageSize, number, phone, status, beginTime, endTime, userId)
    *   **Response:** `Result<PageResult>`

2.  **订单统计 (`statistics`)**
    *   `@ApiOperation(value = "订单统计")`
    *   **HTTP Method:** `GET`, **Path:** `/statistics`
    *   **Response:** `Result<OrderStatisticsVO>` (toBeConfirmed, confirmed, deliveryInProgress)

3.  **订单详情 (`findDetails`)**
    *   `@ApiOperation(value = "订单详情")`
    *   **HTTP Method:** `GET`, **Path:** `/details/{id}`
    *   **Path Variable:** `id` (Long)
    *   **Response:** `Result<OrderVO>` (extends Orders, adds orderDishes, orderDetailList)

4.  **订单接单 (`confirm`)**
    *   `@ApiOperation(value = "订单接单")`
    *   **HTTP Method:** `PUT`, **Path:** `/confirm`
    *   **Request Body:** `OrdersConfirmDTO` (id, status - status may be implicit)
    *   **Response:** `Result`

5.  **订单拒单 (`rejection`)**
    *   `@ApiOperation(value = "订单拒单")`
    *   **HTTP Method:** `PUT`, **Path:** `/rejection`
    *   **Request Body:** `OrdersRejectionDTO` (id, rejectionReason)
    *   **Response:** `Result`

6.  **订单取消 (`cancel`)**
    *   `@ApiOperation(value = "订单取消")`
    *   **HTTP Method:** `PUT`, **Path:** `/cancel`
    *   **Request Body:** `OrdersCancelDTO` (id, cancelReason)
    *   **Response:** `Result`

7.  **订单派送 (`delivery`)**
    *   `@ApiOperation(value = "订单派送")`
    *   **HTTP Method:** `PUT`, **Path:** `/delivery/{id}`
    *   **Path Variable:** `id` (Long)
    *   **Response:** `Result`

8.  **订单完成 (`complete`)**
    *   `@ApiOperation(value = "订单完成")`
    *   **HTTP Method:** `PUT`, **Path:** `/complete/{id}`
    *   **Path Variable:** `id` (Long)
    *   **Response:** `Result`

**前端 API 分析 (`frontend/src/api/order.ts`):**

1.  **查询列表页接口 (`getOrderDetailPage`)**: Path: `/admin/order/conditionSearch`, Method: `get`. (Backend: `/admin/order/conditionSearch`)
2.  **查看接口 (`queryOrderDetailById`)**: Path: `/admin/order/details/${params.orderId}`, Method: `get`. (Backend: `/admin/order/details/${id}`)
3.  **派送接口 (`deliveryOrder`)**: Path: `/admin/order/delivery/${params.id}`, Method: `put`. (Backend: `/admin/order/delivery/${id}`)
4.  **完成接口 (`completeOrder`)**: Path: `/admin/order/complete/${params.id}`, Method: `put`. (Backend: `/admin/order/complete/${id}`)
5.  **订单取消 (`orderCancel`)**: Path: `/admin/order/cancel`, Method: `put`, Data: `{ ...params }`. (Backend: `/admin/order/cancel`)
6.  **接单 (`orderAccept`)**: Path: `/admin/order/confirm`, Method: `put`, Data: `{ ...params }`. (Backend: `/admin/order/confirm`)
7.  **拒单 (`orderReject`)**: Path: `/admin/order/rejection`, Method: `put`, Data: `{ ...params }`. (Backend: `/admin/order/rejection`)
8.  **获取统计数量 (`getOrderListBy`)**: Path: `/admin/order/statistics`, Method: `get`. (Backend: `/admin/order/statistics`)

**`OrderAdminController` 与 `order.ts` 接口初步不一致性总结:**

*   **路径前缀不一致:** 
    *   描述：普遍存在，前端API调用的路径缺少后端Controller类级别定义的 `/admin` 前缀。

*   **路径变量名差异 (`queryOrderDetailById`):**
    *   描述：前端使用 `params.orderId` 来构建路径 `/order/details/${params.orderId}`，而后端 `OrderAdminController` 的 `findDetails` 方法期望路径变量名为 `id` (`/admin/order/details/${id}`).
    *   前端代码示例 (`order.ts`):
        ```typescript
        export const queryOrderDetailById = (params: any) => {
          return request({
            url: `/admin/order/details/${params.orderId}`, // uses params.orderId
            method: 'get'
          })
        }
        ```
    *   后端代码示例 (`OrderAdminController.java`):
        ```java
        @GetMapping("/details/{id}") // expects path variable {id}
        public Result<OrderVO> findDetails(@PathVariable("id") Long id) { /*...*/ }
        ```

*   **DTO 结构对应需确认:**
    *   描述：前端在调用 `orderCancel`, `orderAccept`, `orderReject` 时，使用 `data: { ...params }`。需要确保 `params` 对象中的字段能正确映射到后端 `OrdersCancelDTO` (id, cancelReason), `OrdersConfirmDTO` (id, status - status可能由后端定), 和 `OrdersRejectionDTO` (id, rejectionReason) 的相应字段。

[后续将分析其他Controller和对应的API调用...]

**Controller: `user/UserController.java` (Base Path: `/user/user`)**
 DTOs/VOs 均位于 `sky-pojo/src/main/java/com/sky/{dto,vo}/`

1.  **微信登录 (`login`)**
    *   `@ApiOperation("微信登录")`
    *   **HTTP Method:** `POST`, **Path:** `/login`
    *   **Request Body:** `UserLoginDTO` (code)
    *   **Response:** `Result<UserLoginVO>` (id, openid, token)

**Controller: `user/AddressBookController.java` (Base Path: `/user/addressBook`)**
 Uses `AddressBook` entity as DTO/VO.

1.  **查询地址簿列表 (`findAll`)**
    *   **HTTP Method:** `GET`, **Path:** `/list`
    *   **Response:** `Result<List<AddressBook>>`
2.  **查询默认地址 (`findDefault`)**
    *   **HTTP Method:** `GET`, **Path:** `/default`
    *   **Request Body (Note: GET with @RequestBody):** `AddressBook`
    *   **Response:** `Result<AddressBook>`
3.  **查询地址 (`findById`)**
    *   **HTTP Method:** `GET`, **Path:** `/{id}`
    *   **Path Variable:** `id` (Long)
    *   **Response:** `Result<AddressBook>`
4.  **新增地址 (`saveAddressBook`)**
    *   **HTTP Method:** `POST`, **Path:** (base)
    *   **Request Body:** `AddressBook`
    *   **Response:** `Result`
5.  **修改地址 (`updateAddressBook`)**
    *   **HTTP Method:** `PUT`, **Path:** (base)
    *   **Request Body:** `AddressBook`
    *   **Response:** `Result`
6.  **设置默认地址 (`setDefault`)**
    *   **HTTP Method:** `PUT`, **Path:** `/default`
    *   **Request Body:** `AddressBook`
    *   **Response:** `Result`
7.  **删除地址 (`deleteAddressBook`)**
    *   **HTTP Method:** `DELETE`, **Path:** (base)
    *   **Query Parameter:** `id` (Long)
    *   **Response:** `Result`

**前端 API 分析 (`frontend/src/api/users.ts`):**

*   **`editPassword`**: Path: `/admin/employee/editPassword`, Method: `put`. (Corresponds to `EmployeeController`'s `editPassword`, path needs `/admin` prefix. Should be in `employee.ts`.)
*   **`getStatus`**: Path: `/admin/shop/status`, Method: `get`. (Corresponds to a `ShopController` API, needs prefix and proper file.)
*   **`setStatus`**: Path: `/admin/shop/${status}`, Method: `put`. (Corresponds to a `ShopController` API, path construction and param passing is unclear/non-standard, needs prefix and proper file.)

**`user/UserController`, `user/AddressBookController` 与 `users.ts` 接口初步不一致性总结:**

*   **`users.ts` 内容错位:**
    *   描述：当前 `frontend/src/api/users.ts` 文件内容与用户端的用户及地址簿管理完全不符。它包含了员工密码修改（应在 `employee.ts`）和店铺状态管理（应在专门的 `shop.ts` 或类似文件）的API调用。
    *   员工改密前端代码 (`users.ts`):
        ```typescript
        export const editPassword = (data: any) =>
          request({
            'url': '/admin/employee/editPassword', // Missing /admin prefix
            'method': 'put',
            data
          })
        ```
    *   店铺状态相关前端代码 (`users.ts`):
        ```typescript
        export const getStatus = () =>
          request({
            'url': `/admin/shop/status`, // Missing /admin or /user prefix
            'method': 'get'
          })
        export const setStatus = (status: number) =>
          request({
            'url': `/admin/shop/${status}`, // Path construction unusual, missing prefix
            'method': 'put'
          })
        ```
*   **缺失前端API调用 (用户模块):**
    *   描述：在已分析的前端API文件中（特别是 `users.ts`），尚未找到对应后端 `user/UserController` 的微信登录接口 (`/user/user/login`) 和 `user/AddressBookController` 的各地址簿管理接口的调用。
*   **`AddressBookController.findDefault` 使用 `@RequestBody`:**
    *   描述：后端 `AddressBookController` 的 `findDefault` 方法是一个 `GET` 请求，但使用了 `@RequestBody AddressBook addressBook` 来接收参数。这不符合HTTP GET方法的标准实践，GET请求的参数应通过URL查询参数或路径变量传递。虽然部分服务器可能支持，但这通常被认为是不规范的设计。
    *   后端代码示例 (`AddressBookController.java`):
        ```java
        @GetMapping("/default")
        public Result<AddressBook> findDefault(@RequestBody AddressBook addressBook){ /*...*/ }
        ```

[后续将分析ShopController和对应的API调用...]

**Controller: `admin/ShopController.java` (Base Path: `/admin/shop`)**
 Bean name: `adminShopController`

1.  **设置店铺状态 (`setStatus`)**
    *   `@ApiOperation(value = "设置店铺状态")`
    *   **HTTP Method:** `PUT`, **Path:** `/{status}`
    *   **Path Variable:** `status` (Integer)
    *   **Response:** `Result<String>`

2.  **查询店铺状态 (`getStatus`)**
    *   `@ApiOperation(value = "查询店铺状态")`
    *   **HTTP Method:** `GET`, **Path:** `/status`
    *   **Response:** `Result<Integer>`

**Controller: `user/ShopController.java` (Base Path: `/user/shop`)**
 Bean name: `userShopController`

1.  **查询店铺状态 (`getStatus`)**
    *   **HTTP Method:** `GET`, **Path:** `/status`
    *   **Response:** `Result<Integer>`

**Shop API 与 `frontend/src/api/users.ts` 中相关调用的不一致性总结:**

*   **前端 `getStatus()` (Path: `/shop/status`, Method: `get` in `users.ts`)**
    *   描述：此调用旨在获取店铺状态。
    *   后端对应：
        *   `admin/ShopController`: `GET /admin/shop/status`
        *   `user/ShopController`: `GET /user/shop/status`
    *   不一致性：前端路径 `/shop/status` 缺少 `/admin` 或 `/user` 前缀。根据调用上下文（管理端或用户端功能）应确定正确前缀。
    *   前端代码 (`users.ts`):
        ```typescript
        export const getStatus = () =>
          request({
            'url': `/shop/status`, // Missing /admin or /user prefix
            'method': 'get'
          })
        ```

*   **前端 `setStatus(data)` (Path: `/shop/${status}`, Method: `put`, Data: `data` in `users.ts`)**
    *   描述：此调用旨在设置店铺状态，应对应管理端接口。
    *   后端对应：`admin/ShopController`: `PUT /admin/shop/${status}`
    *   不一致性：
        1.  **路径前缀**: 缺少 `/admin`。
        2.  **路径构造**: 前端将整个 `data` 对象或值拼接到路径中 (`/shop/${status}`)，而后端期望一个代表状态的整数作为路径变量 (`/admin/shop/${status}`).
        3.  **参数传递**: 前端将 `data` 作为请求体发送，而后端 `admin/ShopController.setStatus` 方法仅从路径变量获取状态，不处理请求体。
    *   前端代码 (`users.ts`):
        ```typescript
        export const setStatus = (status: number) =>
          request({
            'url': `/admin/shop/${status}`, // Incorrect path construction, missing /admin
            'method': 'put'
          })
        ```
    *   后端代码 (`admin/ShopController.java`):
        ```java
        @PutMapping("/{status}")
        public Result<String> setStatus(@PathVariable Integer status) { // Expects status from path, no request body
            redisTemplate.opsForValue().set(KEY, status);
            return Result.success("店铺状态设置成功");
        }
        ```
*   **API 调用位置不当:**
    *   描述：这两个店铺相关的API调用位于 `users.ts` 文件中，应迁移到专门的 `shop.ts` 文件中，并区分管理端和用户端调用（如果需要）。

[后续将分析 ReportController (charts.ts) 和 UploadController ...]

**Controller: `admin/ReportController.java` (Base Path: `/admin/report`)**
 VOs 均位于 `sky-pojo/src/main/java/com/sky/vo/`

1.  **营业额统计 (`turnoverStatistics`)**
    *   **HTTP Method:** `GET`, **Path:** `/turnoverStatistics`
    *   **Request Params:** `begin` (LocalDate), `end` (LocalDate)
    *   **Response:** `Result<TurnoverReportVO>` (dateList, turnoverList - comma-separated strings)

2.  **用户统计 (`userStatistics`)**
    *   **HTTP Method:** `GET`, **Path:** `/userStatistics`
    *   **Request Params:** `begin` (LocalDate), `end` (LocalDate)
    *   **Response:** `Result<UserReportVO>` (dateList, totalUserList, newUserList - comma-separated strings)

3.  **订单统计 (`ordersStatistics`)**
    *   **HTTP Method:** `GET`, **Path:** `/ordersStatistics`
    *   **Request Params:** `begin` (LocalDate), `end` (LocalDate)
    *   **Response:** `Result<OrderReportVO>` (dateList, orderCountList, validOrderCountList, totalOrderCount, validOrderCount, orderCompletionRate)

4.  **销量排名TOP10 (`top10`)**
    *   **HTTP Method:** `GET`, **Path:** `/top10`
    *   **Request Params:** `begin` (LocalDate), `end` (LocalDate)
    *   **Response:** `Result<SalesTop10ReportVO>` (nameList, numberList - comma-separated strings)

5.  **导出运营数据报表 (`export`)**
    *   **HTTP Method:** `GET`, **Path:** `/export`
    *   **Response:** `void` (file stream)

**前端 API 分析 (`frontend/src/api/charts.ts`):**
 (路径均以 `/report/...` 开头，缺少 `/admin` 前缀)

*   `getDataes`: `GET /report/amountCollect/{params.date}`
*   `getChartsDataes`: `GET /report/dayCollect/{params.start}/{params.end}`
*   `getDayDataes`: `GET /report/hourCollect/{params.type}/{params.date}`
*   `getDayPayType`: `GET /report/payTypeCollect/{params.date}`
*   `getprivilege`: `GET /report/privilegeCollect/{params.date}`
*   `getSalesRanking`: `GET /report/categoryCollect/{params.type}/{params.date}`
*   `getDayRanking`: `GET /report/currentDishRank/{params.date}`
*   `getTimeQuantumDataes`: `GET /report/dayAmountCollect/{params.type}/${params.start}/${params.end}`
*   `getTimeQuantumReceivables`: `GET /report/datePayTypeCollect/{params.start}/${params.end}`
*   `getTimeQuantumType`: `GET /report/dateCategoryCollect/{params.type}/${params.start}/${params.end}`
*   `getTimeQuantumDishes`: `GET /report/dishRankForDate/{params.start}/${params.end}`
*   `getTimeQuantumDiscount`: `GET /report/privilegeByDate/{params.start}/${params.end}`

**`ReportController` 与 `charts.ts` 接口初步不一致性总结:**

*   **路径前缀不一致:** 
    *   描述：前端所有API调用的路径都缺少后端Controller类级别定义的 `/admin` 前缀 (e.g.,前端用 `/report/...` 而非 `/admin/report/...`).

*   **API端点与后端需求严重不匹配:**
    *   描述：后端提供5个通用统计接口，前端 `charts.ts` 定义了大量细粒度报表接口，两者差异巨大。
    *   例如，前端有 `/report/hourCollect/{type}/{date}` (按小时细分)，后端无直接对应。
    *   前端代码示例 (`charts.ts` - `getChartsDataes`):
        ```typescript
        export const getChartsDataes = (params: any) =>
          request({
            // Path uses path variables for dates
            'url': `/report/dayCollect/${params.start}/${params.end}`,
            'method': 'get'
          })
        ```
    *   后端代码示例 (`ReportController.java` - `turnoverStatistics` uses query params):
        ```java
        @GetMapping("/turnoverStatistics")
        public Result<TurnoverReportVO> turnoverStatistics(
                @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate begin, // Query param
                @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate end) { /*...*/ } // Query param
        ```

*   **缺失前端API调用:**
    *   描述：前端 `charts.ts` 中未发现明确对应后端 `/admin/report/userStatistics`, `/admin/report/ordersStatistics`, `/admin/report/export` 接口的API调用。

*   **潜在的后端API缺失:**
    *   描述：前端 `charts.ts` 中定义的许多具体报表接口（如按支付类型 `getDayPayType`, 按优惠类型 `getprivilege`, 按小时统计 `getDayDataes` 等）在后端 `ReportController` 中没有直接对应的实现方法。这表明后端可能需要新增多个API端点以满足前端图表展示需求。

[后续将分析 UploadController 和其他用户端Controller...]

**Controller: `admin/UploadController.java`**
 (No class-level base path, path defined on method)

1.  **文件上传 (`upload`)**
    *   `@ApiOperation(value = "文件上传")`
    *   **HTTP Method:** `POST`, **Path:** `/admin/common/upload`
    *   **Request Parameter:** `file` (MultipartFile)
    *   **Response:** `Result<String>` (URL) or `Result.error("上传失败")`

**文件上传/下载 API 与 前端 `dish.ts` 中 `commonDownload` 的不一致性总结:**

*   **功能不匹配:**
    *   描述：后端 `UploadController` 仅提供文件上传 (`POST /admin/common/upload`)。前端 `dish.ts` 中的 `commonDownload` (`GET /common/download`) 从名称和方法看是用于文件下载。
    *   前端下载调用 (`dish.ts`):
        ```typescript
        export const commonDownload = (params: any) => {
          return request({
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            },
            url: '/common/download', // Path for download, method GET
            method: 'get',
            params
          })
        }
        ```
    *   后端上传接口 (`UploadController.java`):
        ```java
        @PostMapping("/admin/common/upload")
        public Result upload(MultipartFile file) throws Exception { /* ... */ }
        ```

*   **后端下载接口缺失:**
    *   描述：在已分析的后端 Controller 中，没有找到与前端 `GET /common/download` 调用匹配的下载接口定义。

*   **前端上传接口调用缺失:**
    *   描述：在已分析的前端 API 文件中，尚未发现调用后端 `/admin/common/upload` 上传接口的函数。

*   **`commonDownload` 路径前缀问题:**
    *   描述：前端 `commonDownload` 调用的路径是 `/common/download`。如果这是一个管理端功能，它缺少 `/admin` 前缀；如果是用户端功能，则缺少 `/user` 前缀。其后端实现目前未知。

[后续将分析其他用户端Controller (CategoryQuery, DishQuery, SetMealQuery, ShoppingCart, PayNotify) 和 admin/WorkSpaceController...]

**Controller: `admin/WorkSpaceController.java` (Base Path: `/admin/workspace`)**
 VOs 均位于 `sky-pojo/src/main/java/com/sky/vo/`

1.  **查询工作台数据 (今日) (`businessData`)**
    *   `@ApiOperation(value = "查询工作台数据")`
    *   **HTTP Method:** `GET`, **Path:** `/businessData`
    *   **Response:** `Result<BusinessDataVO>` (turnover, validOrderCount, orderCompletionRate, unitPrice, newUsers)

2.  **查询订单管理数据 (`orderOverView`)**
    *   `@ApiOperation(value = "查询订单管理数据")`
    *   **HTTP Method:** `GET`, **Path:** `/overviewOrders`
    *   **Response:** `Result<OrderOverViewVO>` (waitingOrders, deliveredOrders, completedOrders, cancelledOrders, allOrders)

3.  **查询菜品总览 (`dishOverView`)**
    *   `@ApiOperation(value = "查询菜品总览")`
    *   **HTTP Method:** `GET`, **Path:** `/overviewDishes`
    *   **Response:** `Result<DishOverViewVO>` (sold, discontinued)

4.  **查询套餐总览 (`setmealOverView`)**
    *   `@ApiOperation(value = "查询套餐总览")`
    *   **HTTP Method:** `GET`, **Path:** `/overviewSetmeals`
    *   **Response:** `Result<SetmealOverViewVO>` (sold, discontinued)

**`WorkSpaceController` 与前端接口初步不一致性总结:**

*   **前端API调用缺失/位置不明:**
    *   描述：在当前已分析的各模块API文件 (`employee.ts`, `category.ts`, etc.) 中，未找到明确调用 `/admin/workspace/...` 下这些接口的函数。这些接口主要服务于仪表盘数据展示，其调用可能存在于特定的仪表盘组件内部，或通过 `frontend/src/api/index.ts` 聚合调用。如果存在调用，也需注意 `/admin` 前缀问题。

[后续将分析用户端Controller (CategoryQuery, DishQuery, SetMealQuery, ShoppingCart, PayNotify) 和前端 inform.ts, index.ts ...]

**Controller: `user/CategoryQueryController.java` (Base Path: `/user/category`)**
 Uses `Category` entity as VO.

1.  **查询分类列表 (`findAll`)**
    *   `@ApiOperation(value = "查询分类列表")`
    *   **HTTP Method:** `GET`, **Path:** `/list`
    *   **Query Parameter:** `type` (Integer, optional, 1为菜品分类，2为套餐分类)
    *   **Response:** `Result<List<Category>>`

**`CategoryQueryController` 与前端现有相关调用 (主要在 `dish.ts`, `setMeal.ts`) 的不一致性总结:**

*   **路径前缀不一致:**
    *   描述：前端在 `dish.ts` (`getCategoryList`, `dishCategoryList`) 和 `setMeal.ts` (`dishCategoryList`) 中对 `/category/list` 的调用均缺少 `/user` 前缀。后端实际路径为 `/user/category/list`。
    *   前端代码示例 (`dish.ts` - `getCategoryList`):
        ```typescript
        export const getCategoryList = (params: any) => {
          return request({
            url: '/user/category/list', // Missing /user prefix
            method: 'get',
            params
          })
        }
        ```

*   **API 调用位置不当/冗余:**
    *   描述：用户端获取分类列表的API调用散落在 `dish.ts` 和 `setMeal.ts` 中。`dish.ts` 中甚至有两个功能相似的函数 (`getCategoryList`, `dishCategoryList`)。这些调用应整合并放置在更合适的位置，如 `category.ts` (若它也处理用户端) 或 `index.ts`，并使用统一和清晰的命名。

*   **参数传递方式:**
    *   描述：后端 `findAll` 方法直接接收一个可选的 `Integer type` 查询参数。前端函数如 `getCategoryList(params: any)` 将整个 `params` 对象作为查询参数。这依赖于 `params` 对象中存在一个名为 `type` 的属性才能正确工作。虽然可能碰巧能用，但不够明确。
    *   后端代码 (`CategoryQueryController.java`):
        ```java
        @GetMapping("/list")
        public Result<List<Category>> findAll(Integer type) { /* ... */ }
        ```

[后续将分析用户端 DishQueryController, SetMealQueryController, ShoppingCartController, PayNotifyController 和前端 inform.ts, index.ts ...]

**Controller: `user/DishQueryController.java` (Base Path: `/user/dish`)**
 Uses `DishVO` (structure defined under `admin/DishController` analysis).

1.  **根据分类ID查询菜品列表 (`findByCategoryId`)**
    *   `@ApiOperation(value = "根据分类ID查询菜品列表")`
    *   **HTTP Method:** `GET`, **Path:** `/list`
    *   **Query Parameter:** `categoryId` (Long, required)
    *   **Response:** `Result<List<DishVO>>`

**`DishQueryController` 与前端 `dish.ts` 中 `queryDishList` 的不一致性总结:**

*   **路径前缀不一致:**
    *   描述：前端 `dish.ts` 中的 `queryDishList` 函数调用 `/dish/list`，缺少了后端的 `/user` 前缀。后端实际路径为 `/user/dish/list`。
    *   前端代码示例 (`dish.ts` - `queryDishList`):
        ```typescript
        export const queryDishList = (params: any) => {
          return request({
            url: '/user/dish/list', // Missing /user prefix
            method: 'get',
            params
          })
        }
        ```

*   **参数传递方式:**
    *   描述：后端 `findByCategoryId` 方法期望一个名为 `categoryId` 的 `Long` 类型必需查询参数。前端 `queryDishList(params: any)` 将整个 `params` 对象作为查询参数。这依赖于 `params` 对象中存在一个名为 `categoryId` 的属性且值为 `Long`，或者显式传递 `{ categoryId: params.categoryId }`。
    *   后端代码 (`DishQueryController.java`):
        ```java
        @GetMapping("/list")
        public Result<List<DishVO>> findByCategoryId(Long categoryId) { /* ... */ }
        ```

[后续将分析用户端 SetMealQueryController, ShoppingCartController, PayNotifyController 和前端 inform.ts, index.ts ...]

**Controller: `user/SetMealQueryController.java` (Base Path: `/user/setmeal`)**
 Uses `Setmeal` entity and `DishItemVO`.

1.  **根据分类ID查询套餐列表 (`findByCategoryId`)**
    *   `@ApiOperation(value = "根据分类ID查询套餐列表")`
    *   **HTTP Method:** `GET`, **Path:** `/list`
    *   **Query Parameter:** `categoryId` (Long, required)
    *   **Response:** `Result<List<Setmeal>>`

2.  **根据套餐ID查询套餐内的菜品列表 (`findBySetmealId`)**
    *   `@ApiOperation(value = "根据套餐ID查询套餐内的菜品列表")`
    *   **HTTP Method:** `GET`, **Path:** `/dish/{id}`
    *   **Path Variable:** `id` (Long, 套餐ID)
    *   **Response:** `Result<List<DishItemVO>>`
        *   `DishItemVO` fields: `name` (String), `copies` (Integer), `image` (String), `description` (String)

**`SetMealQueryController` 与前端接口初步不一致性总结:**

*   **前端API调用缺失/位置不明:**
    *   描述：在当前已分析的模块化API文件 (e.g., `setMeal.ts` etc.) 中，未找到明确调用用户端套餐查询接口 (`/user/setmeal/list` 或 `/user/setmeal/dish/{id}`) 的函数。这些调用如果存在，可能位于 `index.ts` 或直接在相关UI组件中实现。如存在调用，需确保路径包含 `/user` 前缀。

[后续将分析用户端 ShoppingCartController, PayNotifyController 和前端 inform.ts, index.ts ...]

**Controller: `user/ShoppingCartController.java` (Base Path: `/user/shoppingCart`)**
 Uses `ShoppingCartDTO` and `ShoppingCart` entity.

1.  **添加购物车 (`saveShoppingCart`)**
    *   **HTTP Method:** `POST`, **Path:** `/add`
    *   **Request Body:** `ShoppingCartDTO` (dishId, setmealId, dishFlavor)
    *   **Response:** `Result`

2.  **查询购物车 (`findAll`)**
    *   **HTTP Method:** `GET`, **Path:** `/list`
    *   **Response:** `Result<List<ShoppingCart>>`

3.  **清空购物车 (`deleteAll`)**
    *   **HTTP Method:** `DELETE`, **Path:** `/clean`
    *   **Response:** `Result`

4.  **减少购物车商品数量 (`subShoppingCart`)**
    *   **HTTP Method:** `POST`, **Path:** `/sub`
    *   **Request Body:** `ShoppingCartDTO` (dishId, setmealId, dishFlavor)
    *   **Response:** `Result`

**`ShoppingCartController` 与前端接口初步不一致性总结:**

*   **前端API调用缺失/位置不明:**
    *   描述：在当前已分析的模块化API文件 (e.g., `dish.ts`, `index.ts` etc.) 中，未找到明确调用用户端购物车接口 (`/user/shoppingCart/...`) 的函数。这些调用如果存在，可能位于用户界面组件内部，或通过 `index.ts` 聚合。如存在调用，需确保路径包含 `/user` 前缀。

[后续将分析用户端 PayNotifyController 和前端 inform.ts, index.ts ...] 

# 实施计划 (由 PLAN 模式生成)

本计划旨在解决 RESEARCH 阶段发现的前后端API不一致问题。修改将涉及前端代码调整、后端代码调整，以及部分情况下两者都需要修改。

**优先级说明:**
*   **P0 (关键):** 阻塞性问题，如错误的API路径、导致请求必定失败的严重参数不匹配。必须最先解决。
*   **P1 (高):** 导致功能性错误或核心功能缺失的问题，如重要API的前端调用缺失、参数处理逻辑错误。
*   **P2 (中):** API设计或代码组织问题，如不规范的API用法 (GET与@RequestBody)、API调用位置不当、命名不规范、后端API路径格式问题。
*   **P3 (低):** 次要问题或优化，如冗余代码、轻微的参数传递不规范 (但目前能工作的情况)。

**A. 全局性修改建议**

1.  **[P0] API路径前缀统一管理:**
    *   **问题**: 前端API调用普遍缺失 `/admin` (管理端) 或 `/user` (用户端) 的路径前缀。
    *   **方案**: 在前端各个API调用函数内部，为URL字符串手动添加正确的前缀 (`/admin` 或 `/user`)。虽然在请求库的baseURL中统一添加部分前缀是一种方法，但考虑到不同API文件可能混杂管理端和用户端调用（如此处`dish.ts`调用用户端分类接口），或者一个API文件本身就应归属特定端（如`category.ts`主要是管理端），在各自函数中明确指定完整路径更为稳妥和清晰，便于问题追踪和后续可能的拆分。
    *   **涉及**: 所有前端API文件中的几乎所有请求。

**B. 后端修改建议 (按Controller组织)**

1.  **`admin/EmployeeController.java`**:
    *   当前无需修改。

2.  **`admin/CategoryController.java`**:
    *   当前无需修改。

3.  **`admin/DishController.java`**:
    *   **[P1] 批量删除菜品 (`deleteDish`) 参数类型处理**:
        *   **问题**: 后端期望 `List<Long> ids`，前端传递的是逗号分隔的字符串 (e.g., "1,2,3")。
        *   **方案**: 修改后端 `deleteDish` 方法，接受 `String ids` 参数，然后在方法内部将逗号分隔的字符串解析为 `List<Long>`。或者，在Controller层使用 `@InitBinder` 或自定义转换器。为了快速修复，先采用方法内解析。
        *   **影响**: `/admin/dish` (DELETE)

4.  **`admin/SetMealController.java`**:
    *   **[P1] 批量删除套餐 (`deleteSetMeal`) 参数类型处理**:
        *   **问题**: 后端期望 `List<Long> ids`，前端传递的是逗号分隔的字符串。
        *   **方案**: 同 `DishController.deleteDish`，修改后端 `deleteSetMeal` 方法，接受 `String ids` 参数，并进行内部解析。
        *   **影响**: `/admin/setmeal` (DELETE)
    *   **[P1] 套餐起售停售 (`updateStatus`) 参数处理**:
        *   **问题**: 前端使用 `params.ids` (可能是单个ID) 作为 `id` 请求参数，后端期望 `Long id`。
        *   **方案**: 后端参数 `Long id` 已经正确，前端传递时确保 `params.ids` 是单个ID值并作为名为 `id` 的查询参数。前端代码侧已按此方式发送 `{ id: params.ids }`，如果 `params.ids` 是单个id，则后端可正确接收。此项主要是确保前端传递的值是单个Long。

5.  **`admin/OrderAdminController.java`**:
    *   当前无需修改 (路径变量名差异将在前端处理)。

6.  **`user/AddressBookController.java`**:
    *   **[P2] 查询默认地址 (`findDefault`) 的 `@RequestBody` 问题**:
        *   **问题**: `GET` 请求 `/user/addressBook/default` 使用了 `@RequestBody AddressBook addressBook`。
        *   **方案**: 修改后端 `findDefault` 方法，移除 `@RequestBody`。如果需要参数（例如用户ID来确定谁的默认地址），应通过 `@RequestParam` 或路径变量传递。鉴于目前没有传递用户ID的逻辑，且通常默认地址是与当前登录用户关联的，该接口可能需要依赖安全上下文获取用户ID，或如果DTO `AddressBook` 中有用户ID字段，则前端应通过查询参数传递。暂定修改为不接收任何参数，依赖服务端从会话获取用户。
        *   **影响**: `GET /user/addressBook/default`

7.  **`admin/ShopController.java`**:
    *   **[P1] 设置店铺状态 (`setStatus`) 参数处理**:
        *   **问题**: 后端 `PUT /admin/shop/{status}` 只接受路径变量 `status`，前端错误地将状态值作为请求体发送，并且路径构造错误。
        *   **方案**: 后端接口定义无误。前端需修正路径构造和参数传递方式（见前端修改部分）。

8.  **`user/ShopController.java`**:
    *   当前无需修改。

9.  **`admin/ReportController.java`**:
    *   **[P1] API端点与前端需求严重不匹配**:
        *   **问题**: 后端提供5个通用统计接口，前端 `charts.ts` 定义了大量细粒度报表接口，两者差异巨大。
        *   **方案**:
            1.  **短期**: 优先确保后端现有5个接口被正确调用（如果前端有对应需求但调用错误）。
            2.  **中期**: 后端根据前端 `charts.ts` 中定义的细化需求，评估并新增API接口。或者，前端调整其图表逻辑，尝试从后端现有通用接口返回的数据中聚合/计算所需指标。
            3.  **本次计划**: 重点修复前端对后端现有5个接口的调用路径和参数问题 (如果前端有对应图表)。对于前端有而后端没有的接口，暂时标记为"后端API缺失"。
        *   **影响**: 所有报表相关API。

10. **`admin/UploadController.java`**:
    *   当前无需修改。

11. **`admin/CommonController` (假设, 用于下载)**:
    *   **[P1] 实现文件下载接口**:
        *   **问题**: 后端未提供 `/common/download` 接口的实现。
        *   **方案**: 在后端新增一个 `CommonController` (或类似命名的Controller)，实现一个 `GET /admin/common/download` (或 `/common/download` 并处理好权限) 的接口，用于文件下载。参数应为文件名或文件标识符。
        *   **影响**: 新增后端接口。

12. **`admin/WorkSpaceController.java`**:
    *   当前无需修改。

13. **`user/CategoryQueryController.java`**:
    *   **[P2] 参数传递方式 (`findAll`)**:
        *   **问题**: 前端将整个 `params` 对象作为查询参数，后端直接接收 `Integer type`。
        *   **方案**: 后端接口可保持不变。前端调用时，确保 `params` 对象中确实包含名为 `type` 的属性且值为 `Integer`，或者显式传递 `{ type: params.type }`。此项风险较低，暂不强制修改后端。

14. **`user/DishQueryController.java`**:
    *   **[P2] 参数传递方式 (`findByCategoryId`)**:
        *   **问题**: 前端将整个 `params` 对象作为查询参数，后端接收 `Long categoryId`。
        *   **方案**: 后端接口可保持不变。前端调用时，确保 `params` 对象中确实包含名为 `categoryId` 的属性且值为 `Long`，或者显式传递 `{ categoryId: params.categoryId }`。此项风险较低，暂不强制修改后端。

15. **`user/SetMealQueryController.java`**:
    *   当前无需修改。

16. **`user/ShoppingCartController.java`**:
    *   当前无需修改。

17. **`user/OrderUserController.java`**:
    *   **[P2] 催单接口路径 (`reminder`)**:
        *   **问题**: `reminder/{id}` 可能缺少前导 `/`，应为 `/{id}/reminder` 或 `/reminder/{id}` (若reminder是资源)。当前定义为 `@PostMapping("reminder/{id}")`，在类路径 `/user/order`下会是 `/user/order/reminder/{id}`，路径本身没问题。无需修改。

18. **`user/PayNotifyController.java`**:
    *   **[N/A]** 此为支付回调接口，由支付网关调用，非前端主动调用。确保其路径 (`/notify/paySuccess`) 和处理逻辑对即可。

**C. 前端修改建议 (按文件组织)**

1.  **`frontend/src/api/employee.ts`**:
    *   **[P0] `login`**: 修改路径为 `/admin/employee/login`。
    *   **[P0] `userLogout`**: 修改路径为 `/admin/employee/logout`。
    *   **[P1] `userLogout`**: 移除请求中的 `params`，因为后端不需要。
    *   **[P0] `getEmployeeList`**: 修改路径为 `/admin/employee/page`。
    *   **[P0] `enableOrDisableEmployee`**: 修改路径，将 `params.status` 正确拼接到 `/admin/employee/status/${params.status}`。
    *   **[P0] `addEmployee`**: 修改路径为 `/admin/employee`。
    *   **[P0] `editEmployee`**: 修改路径为 `/admin/employee`。
    *   **[P0] `queryEmployeeById`**: 修改路径为 `/admin/employee/${id}`。
    *   **[P1] 新增API调用**: 为后端 "修改员工密码" (`PUT /admin/employee/editPassword`) 新增前端API调用函数 `editEmployeePassword(data: PasswordEditDTO)`。

2.  **`frontend/src/api/category.ts`**:
    *   **[P0] `getCategoryPage`**: 修改路径为 `/admin/category/page`。
    *   **[P0] `deleCategory`**: 修改路径为 `/admin/category`。
        *   **[P1] `deleCategory`**: 后端期望单个 `Long id`，前端参数为 `{ id: ids }`，其中 `ids` 是字符串。如果 `ids` 是单个ID字符串，后端Spring或许能转换。但如果 `ids` 意图是批量删除（而后端只支持单个删除），则需调整。假设后端 `deleteCategory(Long id)` 只支持单个删除，前端应传递单个ID。若前端业务逻辑是单个删除，则参数名 `ids` 具有误导性，应为 `id`。
    *   **[P0] `editCategory`**: 修改路径为 `/admin/category`。
    *   **[P0] `addCategory`**: 修改路径为 `/admin/category`。
    *   **[P0] `enableOrDisableEmployee` (应为Category)**:
        *   修改路径为 `/admin/category/status/${params.status}`。
        *   **[P2] 函数重命名**: 将 `enableOrDisableEmployee` 重命名为 `enableOrDisableCategory`。
    *   **[P1] 新增API调用**: 为后端 "根据类型查询分类" (`GET /admin/category/list`) 新增前端API调用函数 `getCategoryListByType(params: { type: number })` (管理端)。

3.  **`frontend/src/api/dish.ts`**:
    *   **[P0] `getDishPage`**: 修改路径为 `/admin/dish/page`。
    *   **[P0] `deleteDish`**: 修改路径为 `/admin/dish`。
        *   **[P1] `deleteDish`**: 前端发送 `params: { ids }` (ids是字符串 "1,2,3")，后端期望 `List<Long>` (或按上述B.3修改后为String)。配合后端B.3的修改，前端发送的字符串形式是可接受的。
    *   **[P0] `editDish`**: 修改路径为 `/admin/dish`。
    *   **[P0] `addDish`**: 修改路径为 `/admin/dish`。
    *   **[P0] `queryDishById`**: 修改路径为 `/admin/dish/${id}`。
    *   **[P2] `getCategoryList`**:
        *   此函数调用 `/category/list`，意图是获取分类列表 (可能用于菜品添加/编辑时的下拉框)。它应调用用户端接口 `GET /user/category/list` (如果是在用户上下文中使用) 或管理端接口 `GET /admin/category/list` (如果是在管理端上下文，如菜品管理页面)。假设是后者，则修改路径为 `/admin/category/list`，并考虑是否应移至 `category.ts`。
        *   **[P0]** (如果调用用户端) 修改路径为 `/user/category/list`，参数 `params` 应确保包含 `type`。
        *   **[P0]** (如果调用管理端) 修改路径为 `/admin/category/list`，参数 `params` 应确保包含 `type`。
    *   **[P0] `queryDishList`**: 修改路径为 `/admin/dish/list` (管理端根据分类ID查菜品)。
    *   **[P1] `commonDownload`**:
        *   此函数用于文件下载，当前调用 `/common/download` (GET)。该后端接口缺失。
        *   **方案**: 待后端实现 `/admin/common/download` (或 `/user/common/download`，取决于上下文) 后，更新此路径。暂定为 `/admin/common/download`。
    *   **[P0] `dishStatusByStatus`**: 修改路径为 `/admin/dish/status/${params.status}`。
    *   **[P2] `dishCategoryList`**: 与 `getCategoryList` 功能重复，且路径和参数处理方式类似。
        *   **[P0]** (如果调用用户端) 修改路径为 `/user/category/list`。
        *   **[P0]** (如果调用管理端) 修改路径为 `/admin/category/list`。
        *   **[P3] 建议删除**: 确认其用途后，若与 `getCategoryList` 完全一致，则删除此冗余函数。

4.  **`frontend/src/api/setMeal.ts`**:
    *   **[P0] `getSetmealPage`**: 修改路径为 `/admin/setmeal/page`。
    *   **[P0] `deleteSetmeal`**: 修改路径为 `/admin/setmeal`。
        *   **[P1] `deleteSetmeal`**: 同 `deleteDish`，配合后端B.4的修改。
    *   **[P0] `editSetmeal`**: 修改路径为 `/admin/setmeal`。
    *   **[P0] `addSetmeal`**: 修改路径为 `/admin/setmeal`。
    *   **[P0] `querySetmealById`**: 修改路径为 `/admin/setmeal/${id}`。
    *   **[P0] `setmealStatusByStatus`**: 修改路径为 `/admin/setmeal/status/${params.status}`。
        *   **[P1] `setmealStatusByStatus`**: 前端发送 `params: { id: params.ids }`。确保 `params.ids` 是单个ID。
    *   **[P2] `dishCategoryList`**: 调用 `/category/list`。
        *   **[P0]** (如果调用用户端) 修改路径为 `/user/category/list`。
        *   **[P0]** (如果调用管理端) 修改路径为 `/admin/category/list`。
        *   考虑移至 `category.ts` 或通用API文件。

5.  **`frontend/src/api/order.ts`**: (主要是管理端订单)
    *   **[P0] `getOrderDetailPage`**: 修改路径为 `/admin/order/conditionSearch`。
    *   **[P0] `queryOrderDetailById`**: 修改路径为 `/admin/order/details/${params.orderId}`。后端期望路径变量名是 `id`，但由于前端已用 `params.orderId`，且路径能唯一匹配，此项可不改后端，或前端调整为 `/admin/order/details/${params.id}` (若`params.id`存在且等于`orderId`)。暂定前端不改变量名，仅加前缀。
    *   **[P0] `deliveryOrder`**: 修改路径为 `/admin/order/delivery/${params.id}`。
    *   **[P0] `completeOrder`**: 修改路径为 `/admin/order/complete/${params.id}`。
    *   **[P0] `orderCancel`**: 修改路径为 `/admin/order/cancel`。
    *   **[P0] `orderAccept`**: 修改路径为 `/admin/order/confirm`。
    *   **[P0] `orderReject`**: 修改路径为 `/admin/order/rejection`。
    *   **[P0] `getOrderListBy`**: 修改路径为 `/admin/order/statistics`。
    *   **[P1] 新增API调用 (用户端)**: 此文件目前是管理端订单。用户端订单API调用缺失 (见 `user/OrderUserController` 部分)，需新增相关API调用函数，可能在新的API文件如 `userOrder.ts` 或 `users.ts` (如果重构)。

6.  **`frontend/src/api/users.ts`**:
    *   **[P2] 内容错位，整体重构/迁移**: 此文件内容混乱。
    *   **[P0] `editPassword`**:
        *   本应在 `employee.ts`。修改路径为 `/admin/employee/editPassword`。
        *   **迁移**: 将此函数移至 `frontend/src/api/employee.ts` (如果上面未新增)。
    *   **[P0] `getStatus` (店铺状态)**:
        *   应有专门的 `shop.ts`。
        *   根据调用上下文确定是 `/admin/shop/status` 还是 `/user/shop/status`。
        *   **迁移**: 移至新的 `shop.ts`。
    *   **[P0] `setStatus` (店铺状态)**:
        *   应在新的 `shop.ts`。
        *   路径修改为 `/admin/shop/${status}` (status从data中取)。
        *   移除请求体 `data`，因为后端不接收。
        *   **迁移**: 移至新的 `shop.ts`。
    *   **[P1] 新增API调用 (用户端)**:
        *   为 `user/UserController` 的微信登录 (`POST /user/user/login`) 新增API调用。
        *   为 `user/AddressBookController` 的7个API (列表, 默认查询, ID查询, 新增, 修改, 设置默认, 删除) 新增API调用。这些应在 `users.ts` (重构后) 或 `addressBook.ts`。
        *   注意 `AddressBookController.findDefault` 后端修改后，前端调用方式。

7.  **`frontend/src/api/charts.ts`**:
    *   **[P0] 路径前缀**: 所有请求路径添加 `/admin` 前缀。
    *   **[P1] API端点与后端严重不匹配**:
        *   **方案**:
            1.  识别前端哪些图表依赖后端已有的5个统计接口 (`turnoverStatistics`, `userStatistics`, `ordersStatistics`, `top10`, `export`)。
            2.  修改这些前端调用，使其正确请求后端对应接口 (路径、参数使用 `begin`, `end` Date类型)。
            3.  对于前端期望但后端缺失的细粒度接口，暂时标记为功能缺失，待后端补充API。不尝试用现有后端接口强行拟合。
    *   **[P1] 新增API调用**:
        *   如果前端有图表对应后端的 `/userStatistics`, `/ordersStatistics`, `/export`，确保有正确的调用函数。

8.  **`frontend/src/api/index.ts` (或其他通用/入口文件)**:
    *   **[P1] 文件上传调用**:
        *   为后端的 `/admin/common/upload` (POST) 新增一个通用的文件上传函数。
    *   **[P1] 缺失的用户端API调用**:
        *   用户端套餐查询: `GET /user/setmeal/list`, `GET /user/setmeal/dish/{id}`。
        *   用户端购物车: `POST /user/shoppingCart/add`, `GET /user/shoppingCart/list`, `DELETE /user/shoppingCart/clean`, `POST /user/shoppingCart/sub`。
        *   这些调用可能在具体页面组件中，但最好有集中的API函数。

9.  **其他前端文件 (`inform.ts`, `index.ts`等)**:
    *   **[P2] 审阅**: 检查这些文件中是否也存在API调用，并应用相同的路径前缀修复和一致性检查。

---
*以下部分由 AI 在协议执行过程中维护*
---