package com.sky.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "Employee", description = "员工信息")
public class Employee implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "员工ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "用户名", example = "zhangsan", required = true)
    private String username;

    @ApiModelProperty(value = "员工姓名", example = "张三", required = true)
    private String name;

    @ApiModelProperty(value = "密码", example = "123456", required = true)
    private String password;

    @ApiModelProperty(value = "手机号", example = "13800138000", required = true)
    private String phone;

    @ApiModelProperty(value = "性别", example = "男", required = true)
    private String sex;

    @ApiModelProperty(value = "身份证号", example = "110101199003070000", required = true)
    private String idNumber;

    @ApiModelProperty(value = "状态，0禁用，1启用", example = "1", required = true)
    private Integer status;

    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间", example = "2023-01-01 12:00:00")
    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人 ID", example = "1")
    private Long createUser;

    @ApiModelProperty(value = "修改人 ID", example = "1")
    private Long updateUser;

}
