package com.sky.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 菜品
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "Dish", description = "菜品信息")
public class Dish implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "菜品ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "菜品名称", example = "宫保鸡丁", required = true)
    private String name;

    @ApiModelProperty(value = "菜品分类ID", example = "1", required = true)
    private Long categoryId;

    @ApiModelProperty(value = "菜品价格", example = "38.00", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "菜品图片", example = "https://example.com/image.jpg")
    private String image;

    @ApiModelProperty(value = "描述信息", example = "经典宫保鸡丁，香而不腌")
    private String description;

    @ApiModelProperty(value = "状态，0停售，1起售", example = "1", required = true)
    private Integer status;

    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人 ID", example = "1")
    private Long createUser;

    @ApiModelProperty(value = "修改人 ID", example = "1")
    private Long updateUser;

}
