package com.sky.vo;

import com.sky.entity.OrderDetail;
import com.sky.entity.Orders;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "OrderVO", description = "订单数据展示对象")
public class OrderVO extends Orders implements Serializable {

    @ApiModelProperty(value = "订单菜品信息", example = "宫保鸡丁*1,鱼香肉丝*1")
    private String orderDishes;

    @ApiModelProperty(value = "订单详情列表")
    private List<OrderDetail> orderDetailList;

}
