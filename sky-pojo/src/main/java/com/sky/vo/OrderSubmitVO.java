package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "OrderSubmitVO", description = "订单提交响应数据对象")
public class OrderSubmitVO implements Serializable {
    @ApiModelProperty(value = "订单ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "订单号", example = "1234567890")
    private String orderNumber;

    @ApiModelProperty(value = "订单金额", example = "88.00")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "下单时间", example = "2023-01-01 12:00:00")
    private LocalDateTime orderTime;
}
