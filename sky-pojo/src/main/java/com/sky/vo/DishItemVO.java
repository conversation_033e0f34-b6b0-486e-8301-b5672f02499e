package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "DishItemVO", description = "套餐内菜品数据展示对象")
public class DishItemVO implements Serializable {

    @ApiModelProperty(value = "菜品名称", example = "宫保鸡丁", required = true)
    private String name;

    @ApiModelProperty(value = "份数", example = "1", required = true)
    private Integer copies;

    @ApiModelProperty(value = "菜品图片", example = "https://example.com/image.jpg")
    private String image;

    @ApiModelProperty(value = "菜品描述", example = "经典宫保鸡丁，香而不腌")
    private String description;
}
