package com.sky.vo;

import com.sky.entity.DishFlavor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "DishVO", description = "菜品数据展示对象")
public class DishVO implements Serializable {

    @ApiModelProperty(value = "菜品ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "菜品名称", example = "宫保鸡丁", required = true)
    private String name;

    @ApiModelProperty(value = "菜品分类ID", example = "1", required = true)
    private Long categoryId;

    @ApiModelProperty(value = "菜品价格", example = "38.00", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "菜品图片", example = "https://example.com/image.jpg")
    private String image;

    @ApiModelProperty(value = "描述信息", example = "经典宫保鸡丁，香而不腌")
    private String description;

    @ApiModelProperty(value = "状态，0停售，1起售", example = "1", required = true)
    private Integer status;

    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "分类名称", example = "热销菜品")
    private String categoryName;

    @ApiModelProperty(value = "菜品关联的口味列表")
    private List<DishFlavor> flavors = new ArrayList<>();

    //private Integer copies;
}
