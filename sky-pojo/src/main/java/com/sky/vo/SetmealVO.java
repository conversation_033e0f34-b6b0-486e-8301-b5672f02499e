package com.sky.vo;

import com.sky.entity.SetmealDish;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SetmealVO", description = "套餐数据展示对象")
public class SetmealVO implements Serializable {

    @ApiModelProperty(value = "套餐ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "分类ID", example = "1", required = true)
    private Long categoryId;

    @ApiModelProperty(value = "套餐名称", example = "商务套餐A", required = true)
    private String name;

    @ApiModelProperty(value = "套餐价格", example = "88.00", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "状态，0停用，1启用", example = "1", required = true)
    private Integer status;

    @ApiModelProperty(value = "描述信息", example = "超值商务套餐，节省时间的选择")
    private String description;

    @ApiModelProperty(value = "套餐图片", example = "https://example.com/image.jpg")
    private String image;

    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "分类名称", example = "商务套餐")
    private String categoryName;

    @ApiModelProperty(value = "套餐和菜品的关联关系列表")
    private List<SetmealDish> setmealDishes = new ArrayList<>();
}
