package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "CategoryDTO", description = "分类数据传输对象")
public class CategoryDTO implements Serializable {

    @ApiModelProperty(value = "分类ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "分类类型，1菜品分类，2套餐分类", example = "1", required = true)
    private Integer type;

    @ApiModelProperty(value = "分类名称", example = "热销菜品", required = true)
    private String name;

    @ApiModelProperty(value = "排序顺序", example = "1", required = true)
    private Integer sort;

}
