package com.sky.dto;

import com.sky.entity.DishFlavor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(value = "DishDTO", description = "菜品数据传输对象")
public class DishDTO implements Serializable {

    @ApiModelProperty(value = "菜品ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "菜品名称", example = "宫保鸡丁", required = true)
    private String name;

    @ApiModelProperty(value = "菜品分类ID", example = "1", required = true)
    private Long categoryId;

    @ApiModelProperty(value = "菜品价格", example = "38.00", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "菜品图片", example = "https://example.com/image.jpg")
    private String image;

    @ApiModelProperty(value = "描述信息", example = "经典宫保鸡丁，香而不腌")
    private String description;

    @ApiModelProperty(value = "状态，0停售，1起售", example = "1", required = true)
    private Integer status;

    @ApiModelProperty(value = "菜品口味列表")
    private List<DishFlavor> flavors = new ArrayList<>();

}
