package com.sky.dto;

import com.sky.entity.SetmealDish;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(value = "SetmealDTO", description = "套餐数据传输对象")
public class SetmealDTO implements Serializable {

    @ApiModelProperty(value = "套餐ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "分类ID", example = "1", required = true)
    private Long categoryId;

    @ApiModelProperty(value = "套餐名称", example = "商务套餐A", required = true)
    private String name;

    @ApiModelProperty(value = "套餐价格", example = "88.00", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "状态，0停用，1启用", example = "1", required = true)
    private Integer status;

    @ApiModelProperty(value = "描述信息", example = "超值商务套餐，节省时间的选择")
    private String description;

    @ApiModelProperty(value = "套餐图片", example = "https://example.com/image.jpg")
    private String image;

    @ApiModelProperty(value = "套餐菜品关系列表")
    private List<SetmealDish> setmealDishes = new ArrayList<>();

}
