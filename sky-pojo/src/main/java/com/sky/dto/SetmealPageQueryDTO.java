package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "SetmealPageQueryDTO", description = "套餐分页查询数据传输对象")
public class SetmealPageQueryDTO implements Serializable {

    @ApiModelProperty(value = "当前页码", example = "1", required = true)
    private int page;

    @ApiModelProperty(value = "每页记录数", example = "10", required = true)
    private int pageSize;

    @ApiModelProperty(value = "套餐名称", example = "商务套餐A")
    private String name;

    @ApiModelProperty(value = "分类ID", example = "1")
    private Integer categoryId;

    @ApiModelProperty(value = "状态，0表示禁用，1表示启用", example = "1")
    private Integer status;

}
