package com.sky.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "CategoryPageQueryDTO", description = "分类分页查询数据传输对象")
public class CategoryPageQueryDTO implements Serializable {

    @ApiModelProperty(value = "当前页码", example = "1", required = true)
    private int page;

    @ApiModelProperty(value = "每页记录数", example = "10", required = true)
    private int pageSize;

    @ApiModelProperty(value = "分类名称", example = "热销菜品")
    private String name;

    @ApiModelProperty(value = "分类类型，1菜品分类，2套餐分类", example = "1")
    private Integer type;

}
