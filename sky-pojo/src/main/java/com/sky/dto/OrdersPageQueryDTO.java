package com.sky.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
//订单分页查询DTO
@Data
public class OrdersPageQueryDTO implements Serializable {

    //页码
    private int page;

    //每页显示记录数
    private int pageSize;

    //订单号
    private String number;

    //手机号
    private String phone;

    //订单状态
    private Integer status;

    //起始时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    //结束时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    //用户id
    private Long userId;

}
