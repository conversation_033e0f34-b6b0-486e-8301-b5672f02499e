<view class="{{['uni-easyinput',(msg)?'uni-easyinput-error':'']}}" style="{{'color:'+(inputBorder&&msg?'#e43d33':styles.color)+';'}}"><view class="{{['uni-easyinput__content',(inputBorder)?'is-input-border':'',(inputBorder&&msg)?'is-input-error-border':'',(type==='textarea')?'is-textarea':'',(disabled)?'is-disabled':'']}}" style="{{'border-color:'+(inputBorder&&msg?'#dd524d':styles.borderColor)+';'+('background-color:'+(disabled?styles.disableColor:'')+';')}}"><block wx:if="{{prefixIcon}}"><uni-icons class="content-clear-icon" vue-id="1b5c5fda-1" type="{{prefixIcon}}" color="#c0c4cc" data-event-opts="{{[['^click',[['onClickIcon',['prefix']]]]]}}" bind:click="__e" bind:__l="__l"></uni-icons></block><block wx:if="{{type==='textarea'}}"><textarea class="{{['uni-easyinput__content-textarea',(inputBorder)?'input-padding':'']}}" name="{{name}}" placeholder="{{placeholder}}" placeholderStyle="{{placeholderStyle}}" disabled="{{disabled}}" placeholder-class="uni-easyinput__placeholder-class" maxlength="{{inputMaxlength}}" focus="{{focused}}" autoHeight="{{autoHeight}}" data-event-opts="{{[['input',[['onInput',['$event']]]],['blur',[['onBlur',['$event']]]],['focus',[['onFocus',['$event']]]],['confirm',[['onConfirm',['$event']]]]]}}" value="{{val}}" bindinput="__e" bindblur="__e" bindfocus="__e" bindconfirm="__e"></textarea></block><block wx:else><input class="uni-easyinput__content-input" style="{{'padding-right:'+(type==='password'||clearable||prefixIcon?'':'10px')+';'+('padding-left:'+(prefixIcon?'':'10px')+';')}}" type="{{type==='password'?'text':type}}" name="{{name}}" password="{{!showPassword&&type==='password'}}" placeholder="{{placeholder}}" placeholderStyle="{{placeholderStyle}}" placeholder-class="uni-easyinput__placeholder-class" disabled="{{disabled}}" maxlength="{{inputMaxlength}}" focus="{{focused}}" confirmType="{{confirmType}}" data-event-opts="{{[['focus',[['onFocus',['$event']]]],['blur',[['onBlur',['$event']]]],['input',[['onInput',['$event']]]],['confirm',[['onConfirm',['$event']]]]]}}" value="{{val}}" bindfocus="__e" bindblur="__e" bindinput="__e" bindconfirm="__e"/></block><block wx:if="{{type==='password'&&passwordIcon}}"><block wx:if="{{val!=''}}"><uni-icons class="{{['content-clear-icon',(type==='textarea')?'is-textarea-icon':'']}}" vue-id="1b5c5fda-2" type="{{showPassword?'eye-slash-filled':'eye-filled'}}" size="{{18}}" color="#c0c4cc" data-event-opts="{{[['^click',[['onEyes']]]]}}" bind:click="__e" bind:__l="__l"></uni-icons></block></block><block wx:else><block wx:if="{{suffixIcon}}"><block wx:if="{{suffixIcon}}"><uni-icons class="content-clear-icon" vue-id="1b5c5fda-3" type="{{suffixIcon}}" color="#c0c4cc" data-event-opts="{{[['^click',[['onClickIcon',['suffix']]]]]}}" bind:click="__e" bind:__l="__l"></uni-icons></block></block><block wx:else><block wx:if="{{clearable&&val!==''&&!disabled}}"><uni-icons class="{{['content-clear-icon',(type==='textarea')?'is-textarea-icon':'']}}" vue-id="1b5c5fda-4" type="clear" size="{{clearSize}}" color="#c0c4cc" data-event-opts="{{[['^click',[['onClear']]]]}}" bind:click="__e" bind:__l="__l"></uni-icons></block></block></block><slot name="right"></slot></view></view>