@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.date-select.data-v-7d30bc3a {
  width: 100%;
  height: 400rpx;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  background-color: #FFFFFF;
  position: relative;
}
.date-select .picker-view.data-v-7d30bc3a {
  background-color: #EEEEEE;
  position: absolute;
  width: 100%;
  height: 288rpx;
  bottom: 20rpx;
}
.date-select .picker-view .item.data-v-7d30bc3a {
  height: 88rpx;
  line-height: 100rpx;
  font-size: 32rpx;
  text-align: center;
}
.date-select .picker-view .item .itemTit.data-v-7d30bc3a {
  font-size: 28rpx;
  line-height: 30rpx;
  padding-top: 18rpx;
}
.date-select .picker-view .item .itemTit.data-v-7d30bc3a:last-child {
  font-size: 24rpx;
}
.date-select .btn.data-v-7d30bc3a {
  width: 100%;
  height: 100rpx;
  box-sizing: border-box;
}
.date-select .btn .btn-left.data-v-7d30bc3a, .date-select .btn .btn-right.data-v-7d30bc3a {
  color: #FFFFFF;
  width: 150rpx;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 15rpx;
}
.demo-flex.data-v-7d30bc3a {
  display: flex;
  align-items: center;
}
.demo-flex .select-time.data-v-7d30bc3a {
  height: 96rpx;
  line-height: 96rpx;
  font-size: 32rpx;
  font-family: "PingFangSC, PingFangSC-Medium";
  font-weight: 500;
  text-align: center;
  color: #323233;
  flex: 1;
  padding-left: 90rpx;
}
.demo-flex .of-sourceof.data-v-7d30bc3a {
  height: 96rpx;
  line-height: 96rpx;
  font-size: 30rpx;
  font-family: "PingFangSC, PingFangSC-Regular";
  text-align: center;
  color: #3388ff;
  padding-right: 32rpx;
}
.picker-item-area.data-v-7d30bc3a {
  width: 100%;
  height: 200rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 30prx;
  font-weight: 500;
  color: #666666;
}
.picker-area.data-v-7d30bc3a {
  width: 100%;
  height: 600rpx;
}
.popup-title.data-v-7d30bc3a {
  text-align: center;
  padding: 40rpx 30rpx;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}
.product-type-area.data-v-7d30bc3a {
  width: 100%;
  background: #FFFFFF;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}

