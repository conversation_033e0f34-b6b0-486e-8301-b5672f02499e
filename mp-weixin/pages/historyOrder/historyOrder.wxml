<view class="history_order data-v-5cf07246"><uni-nav-bar vue-id="411e5780-1" left-icon="back" leftIcon="arrowleft" title="历史订单" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-5cf07246" bind:__l="__l"></uni-nav-bar><scroll-view class="scroll-row data-v-5cf07246" scroll-x="true" scroll-into-view="{{scrollinto}}" scroll-with-animation="{{true}}"><block wx:for="{{tabBars}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="scroll-row-item data-v-5cf07246" id="{{'tab'+index}}" data-event-opts="{{[['tap',[['changeTab',[index]]]]]}}" bindtap="__e"><view class="{{['data-v-5cf07246',tabIndex==index?'scroll-row-item-act':'']}}"><text class="line data-v-5cf07246"></text>{{item}}</view></view></block></scroll-view><swiper style="{{'height:'+(scrollH+'px')+';'}}" current="{{tabIndex}}" data-event-opts="{{[['change',[['onChangeSwiperTab',['$event']]]]]}}" bindchange="__e" class="data-v-5cf07246"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="data-v-5cf07246"><scroll-view style="{{'height:'+(scrollH+'px')+';'}}" scroll-y="true" data-event-opts="{{[['scrolltolower',[['lower',['$event']]]]]}}" bindscrolltolower="__e" class="data-v-5cf07246"><block wx:if="{{recentOrdersList&&recentOrdersList.length>0}}"><view class="main recent_orders data-v-5cf07246"><block wx:for="{{item.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['box','order_lists','data-v-5cf07246',(item.m0+1===recentOrdersList.length)?'item-last':'']}}"><view class="date_type data-v-5cf07246"><text class="time data-v-5cf07246">{{item.$orig.orderTime}}</text><text class="{{['type','status','data-v-5cf07246',(item.$orig.status==2)?'status':'']}}">{{item.m1}}</text></view><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" class="orderBox data-v-5cf07246" bindtap="__e"><view class="food_num data-v-5cf07246"><scroll-view class="pic data-v-5cf07246" style="width:100%;overflow:hidden;white-space:nowrap;" scroll-x="true"><block wx:for="{{item.$orig.orderDetailList}}" wx:for-item="num" wx:for-index="y" wx:key="y"><view class="food_num_item data-v-5cf07246"><view class="img data-v-5cf07246"><image src="{{num.image}}" class="data-v-5cf07246"></image></view><view class="food data-v-5cf07246">{{num.name}}</view></view></block></scroll-view></view><view class="numAndAum data-v-5cf07246"><view class="data-v-5cf07246"><text class="data-v-5cf07246">{{"￥"+item.g0}}</text></view><view class="data-v-5cf07246"><text class="data-v-5cf07246">{{"共"+item.m2.count+"件"}}</text></view></view></view><view class="againBtn data-v-5cf07246"><button class="new_btn data-v-5cf07246" type="default" data-event-opts="{{[['tap',[['oneMoreOrder',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" bindtap="__e">再来一单</button><block wx:if="{{item.m3}}"><button class="new_btn btn data-v-5cf07246" type="default" data-event-opts="{{[['tap',[['goDetail',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" bindtap="__e">去支付</button></block><block wx:if="{{item.$orig.status===2}}"><button class="new_btn btn data-v-5cf07246" type="default" data-event-opts="{{[['tap',[['handleReminder',['center','$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" bindtap="__e">催单</button></block></view></view></block></view></block></scroll-view></swiper-item></block></swiper><uni-popup class="comPopupBox data-v-5cf07246 vue-ref" vue-id="411e5780-2" data-ref="commonPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-5cf07246"><view class="text data-v-5cf07246">{{textTip}}</view><block wx:if="{{showConfirm}}"><view class="btn data-v-5cf07246"><view data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" bindtap="__e" class="data-v-5cf07246">确认</view></view></block></view></uni-popup></view>