<view class="data-v-54d3589c"><uni-nav-bar vue-id="45983bea-1" left-icon="back" leftIcon="arrowleft" title="订单详情" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-54d3589c" bind:__l="__l"></uni-nav-bar><view class="order_content orderDetail data-v-54d3589c"><view class="order_content_box data-v-54d3589c" scroll-y="true" scroll-top="0rpx"><view class="box data-v-54d3589c"><view class="orderInfoTip data-v-54d3589c"><view class="tit data-v-54d3589c">{{$root.m0}}</view><block wx:if="{{!timeout&&orderDetailsData.status===1}}"><view class="data-v-54d3589c"><view class="tit data-v-54d3589c">等待支付</view><view class="time data-v-54d3589c"><view class="timeIcon data-v-54d3589c"></view>等待支付：<text class="data-v-54d3589c">{{rocallTime}}</text><text class="data-v-54d3589c">{{paymentTime}}</text></view></view></block><view class="againBtn data-v-54d3589c"><block wx:if="{{!timeout&&orderDetailsData.status===1||orderDetailsData.status===2||orderDetailsData.status===3||orderDetailsData.status===4}}"><button class="new_btn data-v-54d3589c" type="default" data-event-opts="{{[['tap',[['handleCancel',['center','$0'],['orderDetailsData']]]]]}}" bindtap="__e">取消订单</button></block><block wx:if="{{!timeout&&orderDetailsData.status===1}}"><button class="new_btn btn data-v-54d3589c" type="default" data-event-opts="{{[['tap',[['handlePay',['$0'],['orderDetailsData.id']]]]]}}" bindtap="__e">立即支付</button></block><block wx:if="{{orderDetailsData.status===2}}"><button class="new_btn btn data-v-54d3589c" type="default" data-event-opts="{{[['tap',[['handleReminder',['center','$0'],['orderDetailsData.id']]]]]}}" bindtap="__e">催单</button></block><block wx:if="{{orderDetailsData.status==5}}"><button class="new_btn data-v-54d3589c" type="default" data-event-opts="{{[['tap',[['handleRefund',['center']]]]]}}" bindtap="__e">申请退款</button></block><block wx:if="{{orderDetailsData.status!==7}}"><button class="new_btn data-v-54d3589c" type="default" data-event-opts="{{[['tap',[['oneMoreOrder',['$0'],['orderDetailsData.id']]]]]}}" bindtap="__e">再来一单</button></block></view></view></view><block wx:if="{{!timeout&&orderDetailsData.status===1}}"><view class="box timeTip data-v-54d3589c"><view class="icon newIcon data-v-54d3589c"></view>请在15分钟内完成支付，超时将自动取消。</view></block><block wx:if="{{orderDetailsData.status===6&&orderDetailsData.payStatus===2}}"><view class="box timeTip data-v-54d3589c"><view class="icon moneyIcon data-v-54d3589c"></view>您的订单已<text class="data-v-54d3589c">退款成功</text>。</view></block><view class="box order_list_cont data-v-54d3589c"><view class="order_list data-v-54d3589c"><view class="word_text data-v-54d3589c"><text class="word_style data-v-54d3589c">苍穹餐厅</text></view><view class="order-type data-v-54d3589c"><block wx:for="{{$root.l0}}" wx:for-item="obj" wx:for-index="index" wx:key="index"><view class="type_item data-v-54d3589c"><view class="dish_img data-v-54d3589c"><image class="dish_img_url data-v-54d3589c" mode="aspectFill" src="{{obj.$orig.image}}"></image></view><view class="dish_info data-v-54d3589c"><view class="dish_name data-v-54d3589c">{{obj.$orig.name}}</view><view class="dish_dishFlavor data-v-54d3589c">{{obj.$orig.dishFlavor?obj.$orig.dishFlavor:''}}</view><view class="dish_price data-v-54d3589c">×<block wx:if="{{obj.$orig.number&&obj.$orig.number>0}}"><text class="dish_number data-v-54d3589c">{{obj.$orig.number}}</text></block></view><view class="dish_active data-v-54d3589c"><text class="data-v-54d3589c">￥</text>{{''+obj.g0+''}}</view></view></view></block><view class="iconUp data-v-54d3589c"><block wx:if="{{orderDetailsData.orderDetailList.length>2}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="data-v-54d3589c"><text class="data-v-54d3589c">{{!showDisplay?'展开更多':'点击收起'}}</text><image class="{{['icon_img','data-v-54d3589c',showDisplay?'icon_imgDown':'']}}" src="../../static/toRight.png" mode></image></view></block></view><view class="orderList data-v-54d3589c"><view class="orderInfo data-v-54d3589c"><text class="text data-v-54d3589c">打包费</text><text class="may data-v-54d3589c">￥</text>{{''+orderDetailsData.packAmount+''}}</view><view class="orderInfo data-v-54d3589c"><text class="text data-v-54d3589c">配送费</text><text class="may data-v-54d3589c">￥</text>6</view><view class="totalMoney data-v-54d3589c">合计<text class="text data-v-54d3589c"><text class="data-v-54d3589c">￥</text>{{''+orderDetailsData.amount+''}}</text></view></view></view></view></view><view class="box contactMerchant data-v-54d3589c"><view data-event-opts="{{[['tap',[['handlePhone',['bottom']]]]]}}" bindtap="__e" class="data-v-54d3589c"><view class="phoneIcon data-v-54d3589c"></view>联系商家</view></view><view class="box data-v-54d3589c"><view class="orderBaseInfo data-v-54d3589c"><view class="data-v-54d3589c"><view class="data-v-54d3589c">期望时间</view><view class="data-v-54d3589c">{{orderDetailsData.deliveryStatus===1?'立即送出':orderDetailsData.estimatedDeliveryTime}}</view></view><view class="data-v-54d3589c"><view class="data-v-54d3589c">配送地址</view><view class="data-v-54d3589c"><view class="nameInfo data-v-54d3589c"><text class="data-v-54d3589c">{{orderDetailsData.consignee}}</text>{{''+orderDetailsData.phone+''}}</view><view class="data-v-54d3589c">{{orderDetailsData.address}}</view></view></view></view></view><view class="box data-v-54d3589c"><view class="orderBaseInfo data-v-54d3589c"><view class="data-v-54d3589c"><view class="data-v-54d3589c">订单号码</view><view class="data-v-54d3589c">{{orderDetailsData.number}}</view></view><view class="data-v-54d3589c"><view class="data-v-54d3589c">订单时间</view><view class="data-v-54d3589c">{{orderDetailsData.orderTime}}</view></view><view class="data-v-54d3589c"><view class="data-v-54d3589c">支付方式</view><view class="data-v-54d3589c">{{orderDetailsData.payMethod===1?'微信':'支付宝'}}</view></view><view class="data-v-54d3589c"><view class="data-v-54d3589c">餐具数量</view><view class="data-v-54d3589c">{{orderDetailsData.tablewareStatus===1?orderDetailsData.tablewareNumber:orderDetailsData.tablewareNumber}}</view></view></view></view></view><uni-popup class="comPopupBox data-v-54d3589c vue-ref" vue-id="45983bea-2" data-ref="commonPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-54d3589c"><view class="text data-v-54d3589c">{{textTip}}</view><block wx:if="{{showConfirm}}"><view class="btn data-v-54d3589c"><view data-event-opts="{{[['tap',[['closePopupInfo',['$event']]]]]}}" bindtap="__e" class="data-v-54d3589c">确认</view></view></block><block wx:else><view class="btn data-v-54d3589c"><view data-event-opts="{{[['tap',[['closePopupInfo',['$event']]]]]}}" bindtap="__e" class="data-v-54d3589c">先等等</view><view data-event-opts="{{[['tap',[['handlePhone',['bottom']]]]]}}" bindtap="__e" class="data-v-54d3589c">拨打电话</view></view></block></view></uni-popup><view class="container phoneCon data-v-54d3589c"><uni-popup class="popupBox data-v-54d3589c vue-ref" bind:change="__e" vue-id="45983bea-3" data-ref="phone" data-event-opts="{{[['^change',[['change']]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-54d3589c"><view class="data-v-54d3589c">{{phone}}</view><view data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e" class="data-v-54d3589c">呼叫</view><view data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="closePopup data-v-54d3589c" bindtap="__e">取消</view></view></uni-popup></view></view></view>