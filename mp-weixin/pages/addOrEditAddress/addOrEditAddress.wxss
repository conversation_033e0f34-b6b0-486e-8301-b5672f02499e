@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.add_edit.data-v-174d7646 {
  width: 750rpx;
  height: 100%;
  background-color: #fff;
}
.add_edit .form_address .form_item.data-v-174d7646 {
  margin: 0 22rpx;
  padding: 36rpx 0;
  border-bottom: 1px solid #efefef;
  display: flex;
  align-items: center;
}
.add_edit .form_address .form_item.pad.data-v-174d7646 {
  padding-bottom: 10rpx;
  align-items: baseline;
}
.add_edit .form_address .form_item .title.data-v-174d7646 {
  width: 140rpx;
  opacity: 1;
  font-size: 28rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 600;
  text-align: left;
  color: #333333;
  letter-spacing: 0px;
  line-height: 44rpx;
}
.add_edit .form_address .form_item.data-v-174d7646 .is-input-border {
  border: 0 none;
  border-radius: none;
  min-height: auto;
  line-height: 16rpx;
}
.add_edit .form_address .form_item.data-v-174d7646 .is-input-border .uni-easyinput__content-input {
  padding-left: 0 !important;
  font-size: 26rpx;
}
.add_edit .form_address .form_item.data-v-174d7646 .is-input-border .uni-easyinput__placeholder-class {
  font-size: 26rpx;
}
.add_edit .form_address .form_item.data-v-174d7646 .is-input-border .uni-easyinput__content-textarea {
  padding: 18rpx 0 0;
  width: 100%;
  min-height: 60rpx;
  box-sizing: border-box;
  overflow: visible;
  height: auto;
  font-size: 26rpx;
}
.add_edit .form_address .form_item .uni-input.data-v-174d7646 {
  flex: 1;
}
.add_edit .form_address .form_item.data-v-174d7646 .uni-place {
  font-size: 26rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  color: #999999 !important;
}
.add_edit .form_address .form_item .radio.data-v-174d7646 {
  opacity: 1;
  font-size: 26rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #333333;
  letter-spacing: 0px;
  height: 40rpx;
  display: flex;
  padding-right: 20rpx;
  margin-left: 20rpx;
}
.add_edit .form_address .form_item .radio .radio-item.data-v-174d7646 {
  display: flex;
  align-items: center;
}
.add_edit .form_address .form_item .radio .radio-item.data-v-174d7646:first-child {
  margin-right: 54rpx;
}
.add_edit .form_address .form_item .radio .radio-img.data-v-174d7646 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.add_edit .form_address .form_item .tag_text.data-v-174d7646 {
  width: 68rpx;
  height: 44rpx;
  line-height: 40rpx;
  border: 1px solid #e5e4e4;
  display: inline-block;
  border-radius: 6rpx;
  text-align: center;
  box-sizing: border-box;
  color: #333333;
  font-size: 24rpx;
}
.add_edit .form_address .form_item .tag_text.data-v-174d7646:nth-child(3) {
  margin: 0 20rpx;
}
.add_edit .form_address .form_item .tag_text:nth-child(3).active.data-v-174d7646 {
  background: #fef8e7;
  border: 1px solid #fef8e7;
}
.add_edit .form_address .form_item .tag_text:nth-child(4).active.data-v-174d7646 {
  background: #e7fef8;
  border: 1px solid #e7fef8;
}
.add_edit .form_address .form_item .active.data-v-174d7646 {
  background: #e1f1fe;
  border: 1px solid #e1f1fe;
}
.add_edit .form_address .form_item .addressIcon.data-v-174d7646 {
  width: 24px;
  height: 24px;
  display: inline-block;
  position: absolute;
  right: 20rpx;
  top: 0;
}
.add_edit .form_address .form_item .addressIcon .icon.data-v-174d7646 {
  width: 16px;
  height: 16px;
  display: inline-block;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAjRJREFUWEftlj+LE0EYxp93cq4aI9jpoVikEqsTbMQqjXAW6hVZLCTFZhOENH6DnXyG2GxisiTVuYGTs7grBPFD+EWCimYzeyMTHFlCYGfyh2uy9cy+v+d5n33fJVzyQ5dcHzuAnQNWDnDOGQAWBEFKRHITAbYC0AWllPN7m4AwAlAFVbHBYPBQCFGezWbfWq3WT+UI5/xiHSdyAXSRKIoeCCG+AtgnotPpdPpmExAmAHucc9Htdl+owgB+A7hORGdE9Lper/9Yx4lcAG1/HMfOZDI5BnAE4A+Aa0R07jhOtVar/VoVIhdA9VdDjEajG0mSjKWUhwCmAK6u64QRgILQCvv9/k0p5bGU8nkG4r8TGtY0mMYAWYgFJxIATtYJGwgrgCVOfFxox2ff91+aqp/PEpvD+qxqRxAEstPpOMVi8eRfOwSAvUKh8MrzvFPTUK4MoAZQGIb7jLExgKcAUgAFAM8ajcaXrQHEcVxwXTeNouiOEOIcwAGAGYArUsqw2Wy+3VoGtKper3cPwCcAjwHMQ8gY65bL5ValUhFbAVhQfgbgkS5ORB9832/obNgsKaMMaEV5yk37ng1+LoAuHkXRLSGEUv4kM4BWVq4hcgE453oZHRHRybKer6LcBmC+88MwvM8YU8E7YIy99zzvXbvdJjUPbHq+OHdyHcguo+FweDdN09ulUum767rJOsqNHVj2G7aJwtYA2onxeMyq1erFOrZbfQWr7AqbO0YZsHmh7dkdwF9Q/VIwyc9WMwAAAABJRU5ErkJggg==) no-repeat 6rpx 50%;
  background-size: contain;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  margin-left: 10rpx;
}
.add_edit .form_address .form_item .addressIcon .iconOn.data-v-174d7646 {
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
}
.add_edit .form_address .form_item .update-input.data-v-174d7646 {
  flex: 1;
}
.add_edit .form_address .form_item .update-adress.data-v-174d7646 {
  position: relative;
  line-height: 40rpx;
  padding-bottom: 18rpx;
}
.add_edit .form_address .detail.data-v-174d7646 {
  width: 100%;
  height: 50rpx;
  margin: 0;
}
.add_edit .form_address .detail.data-v-174d7646 .uni-place {
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  color: #999999;
  text-align: left;
  line-height: 50rpx;
}
.add_edit .form_address .detail-ios.data-v-174d7646 {
  padding: 20rpx 14rpx;
}
.add_edit .add_address.data-v-174d7646 {
  margin: 0 auto;
}
.add_edit .add_address button.data-v-174d7646 {
  margin-top: 40rpx;
  margin: 40rpx 18rpx 0 18rpx;
  height: 86rpx;
  line-height: 86rpx;
  border-radius: 8rpx;
  opacity: 1;
  font-size: 30rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 600;
  text-align: center;
  color: #333333;
  letter-spacing: 0px;
  border: 0 none;
}
.add_edit .add_address .add_btn.data-v-174d7646 {
  background: #ffc200;
}
.add_edit .add_address .add_btn .img_btn.data-v-174d7646 {
  width: 44rpx;
  height: 44rpx;
  vertical-align: middle;
  margin-bottom: 8rpx;
}
.add_edit .add_address .del_btn.data-v-174d7646 {
  background: #f6f6f6;
}
.customer-box.data-v-174d7646 {
  height: 100vh;
}
.data-v-174d7646 .uni-icons {
  font-size: 24px !important;
}
.data-v-174d7646 .content-clear-icon {
  display: inline-block;
  width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  padding-top: 4rpx;
  padding-bottom: 4rpx;
}

