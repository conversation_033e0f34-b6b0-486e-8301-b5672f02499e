{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "minified": true, "coverView": true, "es6": false, "postcss": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "uglifyFileName": false, "uploadWithSourceMap": true, "enhance": true, "useMultiFrameRuntime": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "ignoreUploadUnusedFiles": true, "checkInvalidKey": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useStaticServer": true, "showES6CompileOption": false, "useCompilerPlugins": false, "minifyWXML": true, "condition": false, "compileWorklet": false, "localPlugins": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "2.15.0", "appid": "wx4283384ec9ab09d6", "projectname": "sky-take-out", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}}