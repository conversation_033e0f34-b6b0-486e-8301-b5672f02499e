package com.sky.controller.user;

import com.sky.entity.Setmeal;
import com.sky.result.Result;
import com.sky.service.SetMealQueryService;
import com.sky.vo.DishItemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/user/setmeal")
@Slf4j
@Api(tags = "用户端-套餐接口", value = "用户端套餐相关接口", description = "提供套餐查询相关接口")
public class SetMealQueryController {

    @Autowired
    private SetMealQueryService setMealQueryService;

    @ApiOperation(value = "根据分类ID查询套餐列表", notes = "查询指定分类下的所有套餐信息")
    @ApiImplicitParam(name = "categoryId", value = "分类ID", required = true, dataType = "Long", paramType = "query", example = "1")
    @GetMapping("/list")
    @Cacheable(cacheNames = "setmealCache", key = "#categoryId")
    public Result<List<Setmeal>> findByCategoryId(Long categoryId) {
        log.info("查询分类id为{}的套餐", categoryId);
        List<Setmeal> setmeals = setMealQueryService.findByCategoryId(categoryId);
        return Result.success(setmeals);
    }

    @ApiOperation(value = "根据套餐ID查询套餐内的菜品列表", notes = "查询指定套餐包含的所有菜品信息")
    @ApiImplicitParam(name = "id", value = "套餐ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @GetMapping("/dish/{id}")
    @Cacheable(cacheNames = "setmealCache", key = "#id")
    public Result<List<DishItemVO>> findBySetmealId(@PathVariable Long id) {
        log.info("查询套餐id为{}的菜品", id);
        List<DishItemVO> dishItemVOS = setMealQueryService.findBySetmealId(id);
        return Result.success(dishItemVOS);
    }
}
