package com.sky.controller.admin;

import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.CategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/category")
@Slf4j
@Api(tags = "管理端-分类管理", value = "分类相关接口", description = "提供分类的增删改查等相关接口")
public class CategoryController {


    private final CategoryService categoryService;
    @Autowired
    public CategoryController(CategoryService categoryService) {
        this.categoryService = categoryService;
    }

    @ApiOperation(value = "分类分页查询", notes = "根据条件分页查询分类信息")
    @GetMapping("/page")
    public Result<PageResult> findAllByPage(CategoryPageQueryDTO categoryPageQueryDTO) {
        PageResult pageResult = categoryService.findAllByPage(categoryPageQueryDTO);
        return Result.success(pageResult);
    }

    @ApiOperation(value = "新增分类", notes = "新增分类信息")
    @PostMapping
    public Result saveCategory(@RequestBody CategoryDTO categoryDTO) {
        categoryService.saveCategory(categoryDTO);
        return Result.success();
    }

    @ApiOperation(value = "启用禁用分类", notes = "启用或禁用分类")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "status", value = "状态，1启用，0禁用", required = true, dataType = "Integer", paramType = "path"),
        @ApiImplicitParam(name = "id", value = "分类ID", required = true, dataType = "Long", paramType = "query")
    })
    @PostMapping("/status/{status}")
    public Result updateStatus(@PathVariable Integer status, Long id) {
        categoryService.updateStatus(status, id);
        return Result.success();
    }

    @ApiOperation(value = "编辑分类", notes = "编辑分类信息")
    @PutMapping
    public Result updateCategory(@RequestBody CategoryDTO categoryDTO) {
        categoryService.updateCategory(categoryDTO);
        return Result.success();
    }

    @ApiOperation(value = "删除分类", notes = "根据ID删除分类")
    @ApiImplicitParam(name = "id", value = "分类ID", required = true, dataType = "Long", paramType = "query")
    @DeleteMapping
    public Result deleteCategory(Long id) {
        categoryService.deleteCategory(id);
        return Result.success();
    }

    @ApiOperation(value = "根据类型查询分类", notes = "根据分类类型查询分类列表，1为菜品分类，2为套餐分类")
    @ApiImplicitParam(name = "type", value = "分类类型", required = false, dataType = "Integer", paramType = "query", example = "1")
    @GetMapping("/list")
    public Result<List<Category>> findByType(Integer type) {
        List<Category> list = categoryService.findByType(type);
        return Result.success(list);
    }
}
