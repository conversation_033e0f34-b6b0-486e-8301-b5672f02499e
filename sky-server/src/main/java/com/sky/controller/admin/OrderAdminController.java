package com.sky.controller.admin;

import com.sky.dto.OrdersCancelDTO;
import com.sky.dto.OrdersConfirmDTO;
import com.sky.dto.OrdersPageQueryDTO;
import com.sky.dto.OrdersRejectionDTO;
import com.sky.entity.Orders;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.OrderAdminService;
import com.sky.service.impl.OrderAdminServiceImpl;
import com.sky.vo.OrderReportVO;
import com.sky.vo.OrderStatisticsVO;
import com.sky.vo.OrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/order")
@Slf4j
@Api(tags = "管理端-订单管理", value = "订单相关接口", description = "提供订单的增删改查等相关接口")
public class OrderAdminController {

    private final OrderAdminService orderAdminService;
    @Autowired
    public OrderAdminController(OrderAdminService orderAdminService) {
        this.orderAdminService = orderAdminService;
    }


    @GetMapping("/conditionSearch")
    @ApiOperation(value = "订单分页查询", notes = "根据条件分页查询订单信息")
    public Result<PageResult> findByPage(OrdersPageQueryDTO ordersPageQueryDTO) {
        PageResult pageResult = orderAdminService.findByPage(ordersPageQueryDTO);
        return Result.success(pageResult);
    }

    @GetMapping("/statistics")
    @ApiOperation(value = "订单统计", notes = "统计订单状态数量")
    public Result<OrderStatisticsVO> statistics() {
        log.info("订单统计");
        return Result.success(orderAdminService.statistics());
    }

    @GetMapping("/details/{id}")
    @ApiOperation(value = "订单详情", notes = "查询订单详情")
    public Result<OrderVO> findDetails(@PathVariable("id") Long id) {
        log.info("查询订单详情:{}", id);
        return Result.success(orderAdminService.findDetails(id));
    }

    @PutMapping("/confirm")
    @ApiOperation(value = "订单接单", notes = "接单")
    public Result confirm(@RequestBody OrdersConfirmDTO ordersConfirmDTO) {
        log.info("接单:{}", ordersConfirmDTO);
        orderAdminService.confirm(ordersConfirmDTO);
        return Result.success();
    }

    @PutMapping("/rejection")
    @ApiOperation(value = "订单拒单", notes = "拒单")
    public Result rejection(@RequestBody OrdersRejectionDTO ordersRejectionDTO) throws Exception {
        log.info("拒单:{}", ordersRejectionDTO);
        orderAdminService.rejection(ordersRejectionDTO);
        return Result.success();
    }

    @PutMapping("/cancel")
    @ApiOperation(value = "订单取消", notes = "取消订单")
    public Result cancel(@RequestBody OrdersCancelDTO ordersCancelDTO) throws Exception {
        log.info("取消订单:{}", ordersCancelDTO);
        orderAdminService.cancel(ordersCancelDTO);
        return Result.success();
    }

    @PutMapping("/delivery/{id}")
    @ApiOperation(value = "订单派送", notes = "派送订单")
    public Result delivery(@PathVariable("id") Long id) {
        log.info("订单:{}开始配送", id);
        orderAdminService.delivery(id);
        return Result.success();
    }

    @PutMapping("/complete/{id}")
    @ApiOperation(value = "订单完成", notes = "完成订单")
    public Result complete(@PathVariable("id") Long id) {
        log.info("订单:{}完成", id);
        orderAdminService.complete(id);
        return Result.success();
    }
}
