package com.sky.controller.admin;

import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.DishService;
import com.sky.vo.DishVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/admin/dish")
@Slf4j
@Api(tags = "管理端-菜品管理", value = "菜品相关接口", description = "提供菜品的增删改查等相关接口")
public class DishController {

    private final RedisTemplate redisTemplate;
    private final DishService dishService;

    @Autowired
    public DishController(DishService dishService, RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.dishService = dishService;
    }

    @ApiOperation(value = "菜品分页查询", notes = "根据条件分页查询菜品信息")
    @GetMapping("/page")
    public Result<PageResult> findAllByPage(DishPageQueryDTO dishPageQueryDTO) {
        log.info("分页查询菜品");
        PageResult pageResult = dishService.findAllByPage(dishPageQueryDTO);
        return Result.success(pageResult);
    }

    @ApiOperation(value = "新增菜品", notes = "新增菜品及其口味信息")
    @PostMapping
    public Result saveDish(@RequestBody DishDTO dishDTO) {
        log.info("新增菜品");
        dishService.saveDish(dishDTO);
        return Result.success();
    }

    @ApiOperation(value = "批量删除菜品", notes = "根据ID批量删除菜品")
    @ApiImplicitParam(name = "ids", value = "菜品ID列表", required = true, dataType = "List", paramType = "query")
    @DeleteMapping
    public Result deleteDish(@RequestParam("ids") List<Long> ids) {
        log.info("批量删除菜品");
        dishService.deleteDish(ids);
        return Result.success();
    }

    @ApiOperation(value = "根据ID查询菜品", notes = "根据菜品ID查询菜品信息")
    @ApiImplicitParam(name = "id", value = "菜品ID", required = true, dataType = "Long", paramType = "path")
    @GetMapping("/{id}")
    public Result<DishVO> getById(@PathVariable("id") Long id) {
        log.info("根据id查询菜品");
        DishVO dishVO = dishService.findById(id);
        return Result.success(dishVO);
    }

    @ApiOperation(value = "编辑菜品", notes = "编辑菜品及其口味信息")
    @PutMapping
    @CacheEvict(cacheNames = "dishCache",allEntries = true)
    public Result updateDish(@RequestBody DishDTO dishDTO) {
        log.info("修改菜品");
        dishService.updateDish(dishDTO);
        return Result.success();
    }

    @ApiOperation(value = "菜品起售停售", notes = "菜品起售停售操作")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "status", value = "状态，1起售，0停售", required = true, dataType = "Integer", paramType = "path"),
        @ApiImplicitParam(name = "id", value = "菜品ID", required = true, dataType = "Long", paramType = "query")
    })
    @PostMapping("/status/{status}")
    @CacheEvict(cacheNames = "dishCache",allEntries = true)
    public Result updateStatus(@PathVariable Integer status, Long id) {
        log.info("修改菜品状态：{}", status);
        log.info("修改菜品id：{}", id);
        dishService.updateStatus(status, id);
        return Result.success();
    }

    @ApiOperation(value = "根据分类ID查询菜品", notes = "根据分类ID查询菜品列表")
    @ApiImplicitParam(name = "categoryId", value = "分类ID", required = true, dataType = "Long", paramType = "query")
    @GetMapping("/list")
    public Result<List<DishVO>> findByCategoryId(Long categoryId) {
        log.info("根据分类id查询菜品：{}", categoryId);
        List<DishVO> dishVOList = dishService.findByCategoryId(categoryId);
        return Result.success(dishVOList);
    }

    public RedisTemplate getRedisTemplate() {
        return redisTemplate;
    }
}
