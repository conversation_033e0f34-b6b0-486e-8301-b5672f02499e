package com.sky.controller.admin;

import com.sky.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

@RestController("adminShopController")
@Slf4j
@RequestMapping("/admin/shop")
@Api(tags = "管理端-店铺管理", value = "店铺相关接口", description = "提供店铺的启停等相关接口")
public class ShopController {

    public static final String KEY = "SHOP_STATUS";

    @Autowired
    private RedisTemplate redisTemplate;

    @PutMapping("/{status}")
    @ApiOperation(value = "设置店铺状态", notes = "设置店铺状态为营业中或打烊中")
    public Result<String> setStatus(@PathVariable Integer status) {
        log.info("设置店铺状态为:{}", status == 1 ? "营业中" : "打烊中");
        // 将店铺状态保存到redis缓存中
        redisTemplate.opsForValue().set(KEY, status);
        return Result.success("店铺状态设置成功");
    }

    @GetMapping("/status")
    @ApiOperation(value = "查询店铺状态", notes = "查询店铺当前状态")
    public Result<Integer> getStatus() {
        Integer status = (Integer) redisTemplate.opsForValue().get(KEY);
        log.info("查询店铺状态为:{}", status == 1 ? "营业中" : "打烊中");
        return Result.success(status);
    }
}
