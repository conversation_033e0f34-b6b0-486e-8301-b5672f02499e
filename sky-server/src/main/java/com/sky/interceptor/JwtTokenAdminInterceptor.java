package com.sky.interceptor;

import com.sky.constant.JwtClaimsConstant;
import com.sky.context.BaseContext;
import com.sky.properties.JwtProperties;
import com.sky.utils.JwtUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * jwt令牌校验的拦截器
 */
@Component
@Slf4j
public class JwtTokenAdminInterceptor implements HandlerInterceptor {

    private final JwtProperties jwtProperties;

    @Autowired
    public JwtTokenAdminInterceptor(JwtProperties jwtProperties) {
        this.jwtProperties = jwtProperties;
    }

    /**
     * 校验jwt
     */
    //预处理拦截器
    public boolean preHandle(@NonNull HttpServletRequest request,@NonNull HttpServletResponse response, @NonNull Object handler) {
        //判断当前拦截到的是Controller的方法还是其他资源
        if (!(handler instanceof HandlerMethod)) {
            //当前拦截到的不是动态方法，直接放行
            return true;
        }

        //1、从请求头中获取令牌
        String token = request.getHeader(jwtProperties.getAdminTokenName());//能正常工作的原因是 Lombok 的 @Data 注解
        //jwtProperties.getAdminTokenName()方法拿到了 application.yml 中的 sky.jwt.admin-token-name 的值，即 Authorization
        //然后 request.getHeader("Authorization") 就拿到了请求头中的 Authorization 值，即 token

        //2、校验令牌
        try {
            log.info("jwt校验:{}", token);//日志输出令牌
            System.out.println("解析前");
            Claims claims = JwtUtil.parseJWT(jwtProperties.getAdminSecretKey(), token);//解析令牌
            System.out.println("成功解析");
            System.out.println("claims:" + claims);
            Long empId = Long.valueOf(claims.get(JwtClaimsConstant.EMP_ID).toString());//获取员工id
            BaseContext.setCurrentId(empId);//保存员工id到ThreadLocal中
            log.info("当前员工id：{}", empId);//日志输出员工id
            //3、通过，放行
            return true;
        } catch (Exception ex) {
            //4、不通过，响应401状态码
            response.setStatus(401);
            return false;
        }
    }
}
