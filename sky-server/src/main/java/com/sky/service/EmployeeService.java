package com.sky.service;

import com.sky.dto.EmployeeDTO;
import com.sky.dto.EmployeeLoginDTO;
import com.sky.dto.EmployeePageQueryDTO;
import com.sky.dto.PasswordEditDTO;
import com.sky.entity.Employee;
import com.sky.result.PageResult;
import org.springframework.stereotype.Service;


public interface EmployeeService {

    /**
     * 员工登录
     *
     * @param employeeLoginDTO
     * @return
     */
    Employee login(EmployeeLoginDTO employeeLoginDTO);

    PageResult findAllByPage(EmployeePageQueryDTO employeePageQueryDTO);

    void saveEmp(EmployeeDTO employeedto);

    void updateStatus(Integer status, Long id);

    void updateEmp(EmployeeDTO employeeDTO);

    Employee findById(Long id);

    void updatePassword(PasswordEditDTO passwordEditDTO);


}
