package com.sky.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.context.BaseContext;
import com.sky.dto.OrdersPageQueryDTO;
import com.sky.dto.OrdersPaymentDTO;
import com.sky.dto.OrdersSubmitDTO;
import com.sky.entity.*;
import com.sky.exception.AddressBookBusinessException;
import com.sky.exception.OrderBusinessException;
import com.sky.exception.ShoppingCartBusinessException;
import com.sky.mapper.*;
import com.sky.result.PageResult;
import com.sky.service.OrderUserService;
import com.sky.service.WebSocketServer;
import com.sky.utils.HttpClientUtil;
import com.sky.utils.WeChatPayUtil;
import com.sky.vo.OrderPaymentVO;
import com.sky.vo.OrderSubmitVO;
import com.sky.vo.OrderVO;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class OrderUserServiceImpl implements OrderUserService {
    //这个类下包含的方法如下
    //1.用户下单
    //2.订单支付
    //3.支付成功
    //4.历史订单查询
    //5.订单详情查询
    //6.取消订单
    //7.再来一单
    //8.催单




    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private AddressBookMapper addressBookMapper;
    @Autowired
    private ShoppingCartMapper shoppingCartMapper;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private WeChatPayUtil weChatPayUtil;
    @Autowired
    private WebSocketServer webSocketServer;

    private Orders orders;

    @Value("${sky.shop.address}")
    private String shopAddress;

    @Value("${sky.baidu.ak}")
    private String ak;

    @Override
    public OrderSubmitVO submitOrder(OrdersSubmitDTO ordersSubmitDTO) {
        //首先拿到用户的地址簿
        AddressBook addressBook = addressBookMapper.findById(ordersSubmitDTO.getAddressBookId());
        if(addressBook == null){
            throw new AddressBookBusinessException(MessageConstant.ADDRESS_BOOK_IS_NULL);
        }
        //new一个新的购物车
        ShoppingCart shoppingCart = new ShoppingCart();
        //从threadlocal中取出用户的ID
        shoppingCart.setUserId(BaseContext.getCurrentId());
        //取出该用户的购物车里的内容
        List<ShoppingCart> list = shoppingCartMapper.findByUserIdAndType(shoppingCart);
        if(list == null && list.isEmpty()){
            throw new ShoppingCartBusinessException(MessageConstant.SHOPPING_CART_IS_NULL);
        }
        //检查客户的收货地址是否超出配送范围
        // checkOutOfRange(addressBook.getCityName() + addressBook.getDistrictName() + addressBook.getDetail());


        //new一个新的订单
        Orders order = new Orders();
        //向订单中加入数据
        BeanUtils.copyProperties(ordersSubmitDTO,order);
        order.setPhone(addressBook.getPhone());//把手机号加入订单
        order.setAddress(addressBook.getDetail());//详细地址
        order.setConsignee(addressBook.getConsignee());//收货人
        order.setNumber(String.valueOf(System.currentTimeMillis()));//订单号
        order.setUserId(BaseContext.getCurrentId());//下单用户id
        order.setStatus(Orders.PENDING_PAYMENT);//订单状态，待付款
        order.setPayStatus(Orders.UN_PAID);//支付状态，未支付
        order.setOrderTime(LocalDateTime.now());//
        order.setAddress(addressBook.getDetail());//

        orderMapper.saveOrder(order);
        this.orders = order;
        //向订单明细表中插入n条数据
        List<OrderDetail> delList = new ArrayList<>();
        for (ShoppingCart cart : list) {//遍历购物车中的每一条数据
            OrderDetail orderDetail = new OrderDetail();//new一个新的订单明细
            BeanUtils.copyProperties(cart,orderDetail);//把购物车中的数据复制到订单明细中
            orderDetail.setOrderId(order.getId());//订单id
            delList.add(orderDetail);//把订单明细加入订单明细列表中
        }
        orderDetailMapper.saveOrderDetail(delList);

        //清空DQ用户的购物车数据
        shoppingCartMapper.deleteAllByUserId(BaseContext.getCurrentId());

        //封装Vo返回
        OrderSubmitVO orderSubmitVO = OrderSubmitVO.builder()
                .id(order.getId())
                .orderNumber(order.getNumber())
                .orderAmount(order.getAmount())
                .orderTime(order.getOrderTime())
                .build();
        return orderSubmitVO;
    }

    /**
     * 订单支付
     *
     * @param ordersPaymentDTO
     * @return
     */
    public OrderPaymentVO payment(OrdersPaymentDTO ordersPaymentDTO) throws Exception {
        // 当前登录用户id
        Long userId = BaseContext.getCurrentId();
        User user = userMapper.findById(userId);
        Orders orders = orderMapper.getByNumberAndUserId(ordersPaymentDTO.getOrderNumber(), userId);

        //校验订单状态，是否为待支付
        if (orders == null || !orders.getStatus().equals(Orders.PENDING_PAYMENT)) {
            throw new OrderBusinessException(MessageConstant.ORDER_STATUS_ERROR);
        }

        // ------------ START: MOCK PAYMENT LOGIC (模拟支付流程) -------------
        log.info("执行模拟支付流程，订单号: {}. 跳过真实微信支付，直接标记为支付成功。", orders.getNumber());

        // 1. 直接调用 paySuccess 将订单状态修改为已支付，并发送通知
        this.paySuccess(orders.getNumber());

        // 2. 构建一个模拟的 OrderPaymentVO 返回给前端
        OrderPaymentVO vo = new OrderPaymentVO();
        vo.setTimeStamp(String.valueOf(System.currentTimeMillis() / 1000));
        vo.setNonceStr("mockNonceStr-" + System.currentTimeMillis());
        vo.setPackageStr("prepay_id=mock_prepay_id_for_simulation_" + orders.getNumber());
        vo.setSignType("RSA"); 
        vo.setPaySign("mockPaySignForSimulation"); 
        // 设置模拟的总金额 (分)。orders.getAmount() 是 BigDecimal，单位是元。
        if (orders.getAmount() != null) {
            vo.setTotalFee(orders.getAmount().multiply(new BigDecimal("100")).intValue());
        }

        log.info("返回模拟支付参数给前端: {}", JSON.toJSONString(vo));
        return vo;
        // ------------ END: MOCK PAYMENT LOGIC -------------

        /*
        // ------------ START: 原真实支付逻辑 (現已註釋掉) -------------
        //调用微信支付接口，生成预支付交易单
        JSONObject jsonObject = weChatPayUtil.pay(
                ordersPaymentDTO.getOrderNumber(), //商户订单号
                orders.getAmount(), //支付金额，单位 元
                "苍穹外卖订单-" + ordersPaymentDTO.getOrderNumber(), //商品描述
                user.getOpenid() //微信用户的openid
        );


        //
        if (jsonObject.getString("code") != null && jsonObject.getString("code").equals("ORDERPAID")) {
            throw new OrderBusinessException("该订单已支付");
        }
        
        // 微信支付成功返回了预支付交易单，才会包含 paySign，否则可能是错误信息
        if (jsonObject.getString("paySign") == null) {
            log.error("微信支付预下单失败: {}", jsonObject.toString());
            throw new OrderBusinessException(MessageConstant.ORDER_PAYMENT_FAILED + ": " + jsonObject.getString("message"));
        }


        OrderPaymentVO vo = jsonObject.toJavaObject(OrderPaymentVO.class);
        // 前端JSAPI调起支付需要的是 packageValue，而不是 package（关键字冲突）
        //  WeChatPayUtil 返回的是 package，这里需要调整一下以匹配前端或者修改WeChatPayUtil
        //  为了保持WeChatPayUtil的通用性，在此处进行转换
        vo.setPackageStr(jsonObject.getString("package")); 

        // 支付方法只负责生成预支付交易单，不直接修改订单状态
        // 订单状态的修改应该在支付成功的回调通知中处理
        // LocalDateTime check_out_time = LocalDateTime.now();//更新支付时间
        // orderMapper.updateStatus(Orders.TO_BE_CONFIRMED, Orders.PAID, check_out_time, orders.getId());

        // WebSocket 推送逻辑也可以考虑移到支付成功回调，或者保留在这里作为即时通知
        // Map map = new HashMap();
        // map.put("type", 1);//消息类型，1表示来单提醒
        // map.put("orderId", orders.getId());//
        // map.put("content", "订单号：" + ordersPaymentDTO.getOrderNumber());

        // webSocketServer.sendToAllClient(JSON.toJSONString(map));

        return vo;
        // ------------ END: 原真实支付逻辑 -------------
        */
    }


    /**
     * 支付成功，修改订单状态
     *
     * @param outTradeNo
     */
    public void paySuccess(String outTradeNo) {
        // 当前登录用户id
        Long userId = BaseContext.getCurrentId();

        // 根据订单号查询当前用户的订单
        Orders ordersDB = orderMapper.getByNumberAndUserId(outTradeNo, userId);
        
        // 在模拟支付场景下，如果上面getByNumberAndUserId因为userId问题（理论上不应发生在此流程）未查到，
        // 且我们确信订单存在（因为刚创建完），可以尝试仅通过outTradeNo查找，但这需要mapper支持getByNumber。
        // 此处逻辑主要针对PayNotifyController的调用健壮性，对当前模拟影响不大。
        if (ordersDB == null && userId == null) { // 尝试兼容PayNotifyController（userId可能为null）
            log.info("在paySuccess中，尝试通过订单号 {} (无userId)查找订单", outTradeNo);
            // Orders orderSearchedByNumber = orderMapper.getByNumber(outTradeNo); // 假设存在此方法
            // if (orderSearchedByNumber != null) {
            //     ordersDB = orderSearchedByNumber;
            // } else {
            //     log.error("支付成功：通过订单号 {} 也未找到订单。", outTradeNo);
            //     return;
            // }
            // 由于不确定 orderMapper.getByNumber 是否存在，暂时保留原逻辑，但提示风险。
             log.warn("支付成功回调：订单 {} 未找到。若从真实回调触发，需检查订单查找逻辑（当前依赖userId）。", outTradeNo);
             return;
        } else if (ordersDB == null) {
            log.warn("支付成功回调：订单 {} 未找到或不属于用户 {}", outTradeNo, userId);
            return;
        }


        // 根据订单id更新订单的状态、支付方式、支付状态、结账时间
        // 检查订单是否已经是已支付状态，避免重复处理
        if (Orders.PAID.equals(ordersDB.getPayStatus())) {
            log.info("订单 {} 已是支付成功状态，无需重复处理。", outTradeNo);
            return;
        }
        
        Orders ordersToUpdate = Orders.builder()
                .id(ordersDB.getId())
                .status(Orders.TO_BE_CONFIRMED)
                .payStatus(Orders.PAID)
                .checkoutTime(LocalDateTime.now())
                .build();

        orderMapper.update(ordersToUpdate);
        log.info("订单 {} 状态已更新为：待接单，已支付。", outTradeNo);
        
        // 通过WebSocket向商家端发送来单提醒
        Map<String, Object> wsMessageMap = new HashMap<>();
        wsMessageMap.put("type", 1); // 消息类型，1表示来单提醒
        wsMessageMap.put("orderId", ordersDB.getId()); // 订单ID
        wsMessageMap.put("content", "订单号：" + outTradeNo); // 消息内容：订单号
        webSocketServer.sendToAllClient(JSON.toJSONString(wsMessageMap));
        log.info("已发送来单提醒至WebSocket客户端，订单号: {}", outTradeNo);
    }

    @Override
    public PageResult findHistoryOrders(OrdersPageQueryDTO ordersPageQueryDTO) {
        PageHelper.startPage(ordersPageQueryDTO.getPage(), ordersPageQueryDTO.getPageSize());
        ordersPageQueryDTO.setUserId(BaseContext.getCurrentId());
        List<Orders> ordersList = orderMapper.findByPage(ordersPageQueryDTO);
        Page<Orders> p = (Page<Orders>) ordersList;
        List<OrderVO> list = new ArrayList();

        // 查询出订单明细，并封装入OrderVO进行响应
        if (p != null && p.getTotal() > 0) {
            for (Orders orders : p) {//
                Long orderId = orders.getId();// 订单id

                // 查询订单明细
                List<OrderDetail> orderDetails = orderDetailMapper.getByOrderId(orderId);

                OrderVO orderVO = new OrderVO();
                BeanUtils.copyProperties(orders, orderVO);
                orderVO.setOrderDetailList(orderDetails);

                list.add(orderVO);//
            }
        }
        return new PageResult(p.getTotal(), list);
    }

    @Override
    public OrderVO findOrderDetailById(Long id) {
        Orders orders = orderMapper.findById(id);
        OrderVO orderVO = new OrderVO();
        BeanUtils.copyProperties(orders, orderVO);//
        List<OrderDetail> orderDetails = orderDetailMapper.getByOrderId(orders.getId());
        orderVO.setOrderDetailList(orderDetails);
        return orderVO;
    }

    @Override
    public void cancelOrder(Long id) throws Exception {
        Orders orders = orderMapper.findById(id);
        if (orders == null) {
            throw new OrderBusinessException(MessageConstant.ORDER_NOT_FOUND);
        }
        if (orders.getStatus() > 2) {
            throw new OrderBusinessException(MessageConstant.ORDER_STATUS_ERROR);
        }
        //订单状态 1待付款 2待接单 3已接单 4派送中 5已完成 6已取消
        //待支付和待接单状态下，用户可以取消订单
        if(orders.getStatus().equals(Orders.PENDING_PAYMENT) || orders.getStatus().equals(Orders.TO_BE_CONFIRMED)){
            //如果订单已支付，需要进行退款处理
            if(orders.getPayStatus().equals(Orders.PAID)){
                //调用微信支付退款接口
                try {
                    weChatPayUtil.refund(
                            orders.getNumber(), //商户订单号
                            orders.getNumber(), //商户退款单号, 这里可以考虑使用更唯一的退款单号
                            orders.getAmount(),//退款金额，单位 元
                            orders.getAmount());//原订单金额
                    orders.setPayStatus(Orders.REFUND); //标记为已退款
                } catch (Exception e) {
                    log.error("微信退款失败: {}", e.getMessage());
                    // 根据实际业务需求，这里可能需要抛出异常或者记录退款失败状态，以便后续处理
                    throw new OrderBusinessException(MessageConstant.ORDER_REFUND_FAILED);
                }
            }
        } else { // 其他状态不允许用户直接取消，或者有不同的处理逻辑
            throw new OrderBusinessException(MessageConstant.ORDER_STATUS_ERROR);
        }

        orders.setStatus(Orders.CANCELLED);
        orders.setCancelReason("用户取消了订单");
        orders.setCancelTime(LocalDateTime.now());
        orderMapper.update(orders);
    }

    @Override
    public void repetition(Long id) {
        List<OrderDetail> list = orderDetailMapper.getByOrderId(id);
        List<ShoppingCart> shoppingCartList = new ArrayList<>();
        for (OrderDetail orderDetail : list) {
            ShoppingCart shoppingCart = new ShoppingCart();
            BeanUtils.copyProperties(orderDetail, shoppingCart);
            shoppingCart.setUserId(BaseContext.getCurrentId());
            shoppingCart.setCreateTime(LocalDateTime.now());
            shoppingCartList.add(shoppingCart);
        }
        shoppingCartMapper.deleteAllByUserId(BaseContext.getCurrentId());
        shoppingCartMapper.saveShoppingCartWithList(shoppingCartList);
    }

    @Override
    public void reminder(Long id) {
        Orders orders = orderMapper.findById(id);
        Map map = new HashMap();
        map.put("type", 2);
        map.put("orderId", id);
        map.put("content", "订单号：" + orders.getNumber());

        webSocketServer.sendToAllClient(JSON.toJSONString(map));
    }

    /**
     * 检查客户的收货地址是否超出配送范围
     * @param address
     */
    private void checkOutOfRange(String address) {
        Map map = new HashMap();
        map.put("address",shopAddress);
        map.put("output","json");
        map.put("ak",ak);

        //获取店铺的经纬度坐标
        String shopCoordinate = HttpClientUtil.doGet("https://api.map.baidu.com/geocoding/v3", map);

        JSONObject jsonObject = JSON.parseObject(shopCoordinate);
        if(!jsonObject.getString("status").equals("0")){
            //TODO 店铺地址失败
            throw new OrderBusinessException("店铺地址解析失败");
        }

        //数据解析
        JSONObject location = jsonObject.getJSONObject("result").getJSONObject("location");
        String lat = location.getString("lat");
        String lng = location.getString("lng");
        //店铺经纬度坐标
        String shopLngLat = lat + "," + lng;

        map.put("address",address);
        //获取用户收货地址的经纬度坐标
        String userCoordinate = HttpClientUtil.doGet("https://api.map.baidu.com/geocoding/v3", map);

        jsonObject = JSON.parseObject(userCoordinate);
        if(!jsonObject.getString("status").equals("0")){
            throw new OrderBusinessException("收货地址解析失败");
        }

        //数据解析
        location = jsonObject.getJSONObject("result").getJSONObject("location");
        lat = location.getString("lat");
        lng = location.getString("lng");
        //用户收货地址经纬度坐标
        String userLngLat = lat + "," + lng;

        map.put("origin",shopLngLat);
        map.put("destination",userLngLat);
        map.put("steps_info","0");

        //路线规划
        String json = HttpClientUtil.doGet("https://api.map.baidu.com/directionlite/v1/driving", map);

        jsonObject = JSON.parseObject(json);
        if(!jsonObject.getString("status").equals("0")){
            throw new OrderBusinessException("配送路线规划失败");
        }

        //数据解析
        JSONObject result = jsonObject.getJSONObject("result");
        JSONArray jsonArray = (JSONArray) result.get("routes");
        Integer distance = (Integer) ((JSONObject) jsonArray.get(0)).get("distance");

        if(distance > 5000){
            //配送距离超过5000米
            throw new OrderBusinessException("超出配送范围");
        }
    }
}
