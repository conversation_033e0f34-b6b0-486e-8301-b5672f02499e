<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SetMealMapper">
    <insert id="saveSetMeal" useGeneratedKeys="true" keyProperty="id">
        insert into setmeal (id, category_id, name, price, status, description, image, create_time, update_time,
                             create_user, update_user)
        values (#{id}, #{categoryId}, #{name}, #{price}, #{status}, #{description}, #{image}, #{createTime},
                #{updateTime}, #{createUser}, #{updateUser})
    </insert>
    <update id="updateSetMeal">
        update setmeal
        <set>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="image != null">
                image = #{image},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findByCategoryId" resultType="java.lang.Long">
        select category_id
        from setmeal
        where category_id = #{id}
    </select>
    <select id="findAllByPage" resultType="com.sky.vo.SetmealVO">
        select s.*, c.name as categoryName
        from setmeal s
        left join category c
        on s.category_id = c.id
        <where>
            <if test="name != null and name != ''">
                and s.name like concat('%',#{name},'%')
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
            <if test="status != null">
                and s.status = #{status}
            </if>
        </where>
        order by s.create_time desc
    </select>
    <select id="findById" resultType="com.sky.vo.SetmealVO">
        select s.*, c.name as categoryName
        from setmeal s
                 left join category c
                           on s.category_id = c.id
        where s.id = #{id}
    </select>
    <select id="findByCategoryIdToList" resultType="com.sky.entity.Setmeal">
        select * from setmeal
        <where>
            <if test="name != null">
                and name like concat('%',#{name},'%')
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>
    <select id="findBySetmealId" resultType="com.sky.vo.DishItemVO">
        select sd.name, sd.copies, d.image, d.description
        from setmeal_dish sd left join dish d on sd.dish_id = d.id
        where sd.setmeal_id = #{setmealId}
    </select>
    <select id="countByMap" resultType="java.lang.Integer">
        select count(id) from setmeal
        <where>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
        </where>
    </select>
</mapper>
