<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SetMealDishMapper">
<!--    通过id列表查询包含这些菜品的套餐-->
    <select id="findByDishIds" resultType="java.lang.Long">
        select setmeal_id
        from setmeal_dish
        where dish_id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
<!--     通过单个id查询包含这个菜品的套餐-->
    <select id="findByDishId" resultType="com.sky.entity.SetmealDish">
        select *
        from setmeal_dish
        where setmeal_id = #{id}
    </select>
<!--     保存套餐-->
    <insert id="saveSetMealDish">
        insert into setmeal_dish (setmeal_id, dish_id, name, price, copies)
        values
        <foreach collection="setMealDishes" item="sd" separator=",">
            (#{sd.setmealId},#{sd.dishId},#{sd.name},#{sd.price},#{sd.copies})
        </foreach>
    </insert>
</mapper>
