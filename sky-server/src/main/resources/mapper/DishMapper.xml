<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.DishMapper">

    <insert id="saveDish" useGeneratedKeys="true" keyProperty="id">
        insert into dish (name, category_id, price, image, description, create_time, update_time, create_user,
                          update_user, status)
        values (#{name}, #{categoryId}, #{price}, #{image}, #{description}, #{createTime}, #{updateTime}, #{createUser},
                #{updateUser}, #{status})
    </insert>
    <update id="updateDish">
        update dish
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="price != null">price = #{price},</if>
            <if test="image != null">image = #{image},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
        </set>
        where id = #{id}
    </update>

    <select id="findAllByPage" resultType="com.sky.vo.DishVO">
        select d.*, c.name as categoryName
        from dish d
        left outer join category c
        on d.category_id = c.id
        <where>
            <if test="name != null">
                and d.name like concat('%',#{name},'%')</if>
            <if test="categoryId != null">
                and d.category_id = #{categoryId}</if>
            <if test="status != null">
                and d.status = #{status}</if>
        </where>
        order by d.create_time desc
    </select>
    <select id="findByCategoryId" resultType="java.lang.Long">
        select category_id
        from dish
        where category_id = #{id}
    </select>
    <select id="findByCategoryIdToProject" resultType="com.sky.vo.DishVO">
        select d.*, c.name as categoryName
        from dish d
        left outer join category c
        on d.category_id = c.id
        where d.category_id = #{id}
    </select>
    <select id="findById" resultType="com.sky.entity.Dish">
        SELECT * from dish where id = #{id}
    </select>
    <select id="findByCategoryIdWithFlavor" resultType="com.sky.vo.DishVO">
        select d.*, c.name as categoryName
        from dish d
        left outer join category c
        on d.category_id = c.id
        where d.category_id = #{id}
    </select>
    <select id="findByCategoryIdToList" resultType="com.sky.vo.DishVO">
        select d.*,c.name
        from dish d
        left join category c
        on d.category_id = c.id
        <where>
            <if test="categoryId != null">
                and d.category_id = #{categoryId}
            </if>
            <if test="status != null">
                and d.status = #{status}
            </if>
        </where>
    </select>
    <select id="countByMap" resultType="java.lang.Integer">
        select count(id) from dish
        <where>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
        </where>
    </select>
</mapper>
