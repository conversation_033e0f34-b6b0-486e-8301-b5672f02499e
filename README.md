# 苍穹外卖平台 (Sky Take-out)

<div align="center">
    <img src="https://github.com/yourusername/sky-take-out/raw/master/doc/images/logo.png" alt="苍穹外卖平台" width="250">
    <p>
        基于Spring Boot + Vue.js 的多端外卖点餐系统
        <br>
        <a href="http://example.com/demo"><strong>查看演示 »</strong></a>
    </p>
</div>

## 📋 目录

- [项目简介](#项目简介)
- [功能模块](#功能模块)
- [技术架构](#技术架构)
- [项目结构](#项目结构)
- [开发环境](#开发环境)
- [快速开始](#快速开始)
- [功能演示](#功能演示)
- [项目亮点](#项目亮点)
- [常见问题](#常见问题)
- [开发规范](#开发规范)
- [版本更新](#版本更新)
- [开发贡献](#开发贡献)
- [许可证](#许可证)

## 🚀 项目简介

苍穹外卖是一个全功能的在线外卖点餐平台，采用前后端分离的架构设计，支持移动端用户点餐和商家后台管理功能。本系统具备用户登录、商品浏览、购物车、下单支付、订单管理等用户端功能，以及员工管理、分类管理、菜品管理、套餐管理、订单管理、数据统计等商家端功能。

### 📌 项目背景

随着外卖行业的蓬勃发展，传统餐饮企业数字化转型需求日益增长。本项目旨在为中小餐饮商家提供一套完整的在线点餐解决方案，帮助商家低成本实现线上运营，提升用户体验和管理效率。

### 💡 项目特色

- **双端支持**: 提供用户小程序和商家管理平台两套系统
- **全流程覆盖**: 从浏览商品到下单支付，从订单接收到配送完成
- **数据分析**: 提供多维度的经营数据分析，辅助商家决策
- **安全高效**: 采用JWT认证，Redis缓存等技术保障系统安全与性能
- **高可扩展**: 模块化设计，方便功能扩展与二次开发

## 🔍 功能模块

### 用户端功能
* **微信登录** - 基于微信小程序授权登录系统
* **商品浏览** - 查看店铺菜品分类、菜品详情和套餐信息
* **购物车** - 添加、删除、清空购物车商品
* **地址管理** - 维护多个收货地址信息
* **在线下单** - 提交订单并在线支付（微信支付）
* **订单管理** - 查询历史订单、取消订单、再来一单

### 管理端功能
* **员工管理** - 员工账号的添加、查询、禁用、编辑和密码重置
* **分类管理** - 菜品分类和套餐分类的增删改查
* **菜品管理** - 菜品信息维护（包含多种口味规格）
* **套餐管理** - 套餐信息维护（包含多个菜品）
* **订单管理** - 接单、拒单、派送、完成、取消等订单状态管理
* **数据统计** - 营业额统计、用户统计、订单统计、销量排名统计
* **工作台** - 待接单、待派送、已完成等状态订单管理

## 💻 技术架构

### 系统架构图

```
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│                 │       │                 │       │                 │
│  移动端用户界面  │◀─────▶│   后端服务集群   │◀─────▶│  管理端用户界面  │
│  (微信小程序)    │       │ (Spring Boot)   │       │  (Vue.js)       │
│                 │       │                 │       │                 │
└─────────────────┘       └────────┬────────┘       └─────────────────┘
                                   │
                          ┌────────┴────────┐
                          │                 │
                          │  数据持久层      │
                          │ (MySQL + Redis) │
                          │                 │
                          └─────────────────┘
```

### 后端技术栈
* **基础框架**：Spring Boot 2.7.3
* **持久层**：MyBatis + MySQL 8.0.30
* **数据库连接池**：Druid 1.2.1
* **缓存技术**：Redis + Spring Cache
* **身份认证**：JWT (JSON Web Token)
* **接口文档**：Knife4j 3.0.2（基于OpenAPI 3）
* **文件存储**：阿里云OSS对象存储
* **微信支付**：微信支付APIv3
* **工具库**：Lombok、Fastjson、Apache Commons等
* **定时任务**：Spring Task
* **消息推送**：WebSocket

### 前端技术（移动端/管理端）
* **移动端**：微信小程序
* **管理端**：Vue.js + Element UI + Axios + Webpack

## 微信支付模块详解

本项目的微信支付功能基于微信支付APIv3实现，主要包含以下流程：用户下单支付、支付回调处理和用户取消订单退款。

### 1. 核心组件

*   `WeChatPayUtil.java`: 微信支付的核心工具类，封装了与微信支付API的交互，包括：
    *   构建和发送HTTP请求到微信支付服务端。
    *   加载商户私钥和微信支付平台证书，进行签名和验签。
    *   提供 `pay` 方法用于JSAPI下单，生成预支付交易单，并进行二次签名以供小程序调起支付。
    *   提供 `refund` 方法用于发起退款请求。
*   `OrderUserServiceImpl.java`: 订单服务实现类，在用户发起支付请求时，调用 `WeChatPayUtil.pay()` 生成预支付信息；在用户取消已支付订单时，调用 `WeChatPayUtil.refund()` 进行退款。
*   `PayNotifyController.java`: 接收微信支付的异步回调通知。
    *   对收到的加密通知进行解密。
    *   调用 `OrderUserService.paySuccess()` 更新订单状态。
    *   向微信支付系统返回处理成功的响应。
*   `WeChatProperties.java`: 微信支付相关的配置类，通过 `application.yml` 文件注入配置信息，如 `appid`, `mchid`, `privateKeyFilePath`, `apiV3Key` 等。

### 2. 支付流程

```mermaid
sequenceDiagram
    participant UserApp as 用户小程序
    participant Server as 后端服务
    participant WeChatPay as 微信支付系统

    UserApp->>+Server: POST /user/order/submit (提交订单)
    Server->>Server: 创建订单 (状态：待支付)
    Server-->>-UserApp: 返回订单信息 (含订单号)

    UserApp->>+Server: POST /user/order/payment (请求支付)
    Server->>Server: 获取用户信息 (openid)
    Server->>Server: 获取订单信息 (金额、订单号)
    Server->>WeChatPayUtil: 调用 pay(订单号, 金额, openid)
    WeChatPayUtil->>+WeChatPay: POST /v3/pay/transactions/jsapi (JSAPI下单)
    WeChatPay-->>-WeChatPayUtil: 返回预支付交易单 (prepay_id等)
    WeChatPayUtil->>Server: 返回包含 prepay_id 的JSON
    Server->>Server: 二次签名 (生成 paySign 等)
    Server-->>-UserApp: 返回支付参数 (timeStamp, nonceStr, package, signType, paySign)

    UserApp->>WeChatPay: wx.requestPayment(支付参数) (调起微信支付)
    WeChatPay-->>UserApp: 用户完成支付/取消支付

    alt 用户支付成功
        WeChatPay->>+Server: POST /notify/paySuccess (支付结果异步通知)
        Server->>Server: 解密通知数据
        Server->>Server: OrderUserService.paySuccess(订单号) (更新订单状态为"待接单/已支付")
        Server-->>-WeChatPay: 返回成功响应
        UserApp->>Server: (可选)前端轮询或根据微信支付结果查询订单状态
        Server-->>UserApp: 返回最新订单状态
    else 用户支付失败/取消
        UserApp->>Server: (可选)前端查询订单状态
        Server-->>UserApp: 返回订单状态 (仍为"待支付")
    end
```

### 3. 退款流程 (用户取消已支付订单)

```mermaid
sequenceDiagram
    participant UserApp as 用户小程序
    participant Server as 后端服务
    participant WeChatPay as 微信支付系统

    UserApp->>+Server: POST /user/order/cancel/{id} (取消订单)
    Server->>Server: 校验订单状态和支付状态
    alt 订单已支付
        Server->>WeChatPayUtil: 调用 refund(订单号, 退款单号, 退款金额, 原订单金额)
        WeChatPayUtil->>+WeChatPay: POST /v3/refund/domestic/refunds (申请退款)
        WeChatPay-->>-WeChatPayUtil: 返回退款申请结果
        alt 退款申请成功
            WeChatPayUtil->>Server: 返回成功信息
            Server->>Server: 更新订单状态为"已取消"，支付状态为"已退款"
            Server-->>-UserApp: 返回取消成功
            WeChatPay->>+Server: POST /notify/refundSuccess (退款结果异步通知 - 根据实际配置)
            Server->>Server: 处理退款回调，更新退款状态 (如需)
            Server-->>-WeChatPay: 返回成功响应
        else 退款申请失败
            WeChatPayUtil->>Server: 返回失败信息
            Server->>Server: 记录退款失败日志，可能抛出异常
            Server-->>-UserApp: 返回取消失败 (或提示退款失败)
        end
    else 订单未支付
        Server->>Server: 更新订单状态为"已取消"
        Server-->>-UserApp: 返回取消成功
    end
```

### 4. 配置项

确保在 `application.yml` (或对应的环境配置文件 `application-dev.yml`, `application-prod.yml`) 中正确配置以下微信支付相关参数：

```yaml
sky:
  wechat:
    appid: 你的微信小程序appid
    mchid: 你的微信支付商户号
    mchSerialNo: 你的商户API证书序列号 (用于APIv3请求头)
    privateKeyFilePath: 你的商户API私钥文件路径 (如：/path/to/apiclient_key.pem)
    apiV3Key: 你的APIv3密钥 (在商户平台设置)
    weChatPayCertFilePath: 你的微信支付平台证书文件路径 (如：/path/to/wechatpay_cert.pem)
    notifyUrl: 支付成功回调通知地址 (公网可访问的URL，如：https://yourdomain.com/notify/paySuccess)
    refundNotifyUrl: 退款成功回调通知地址 (公网可访问的URL，如：https://yourdomain.com/notify/refundSuccess) # 可选，如果微信支付配置了退款回调
```

**注意：**
*   `privateKeyFilePath` 和 `weChatPayCertFilePath` 需要指向正确的证书文件路径。
*   `notifyUrl` 和 `refundNotifyUrl` 必须是公网可以访问的地址，微信支付服务器才能将通知发送到这些URL。
*   确保微信支付商户平台配置的APIv3密钥与 `apiV3Key` 一致。
*   商户API证书序列号 `mchSerialNo` 用于构建APIv3请求头中的 `Wechatpay-Serial`，需要从商户证书中获取。

## 📁 项目结构

```
sky-take-out/                    # 项目根目录
├── sky-common/                  # 通用模块
│   ├── src/main/java/com/sky/
│   │   ├── constant/            # 常量定义
│   │   ├── context/             # 上下文工具
│   │   ├── exception/           # 异常处理
│   │   ├── json/                # JSON处理工具
│   │   ├── result/              # 统一返回结果
│   │   └── utils/               # 工具类
├── sky-pojo/                    # 数据模型模块
│   ├── src/main/java/com/sky/
│   │   ├── dto/                 # 数据传输对象
│   │   ├── entity/              # 实体类
│   │   ├── vo/                  # 视图对象
│   │   └── enumeration/         # 枚举类型
├── sky-server/                  # 核心业务模块
│   ├── src/main/java/com/sky/
│   │   ├── aspect/              # AOP切面
│   │   ├── config/              # 配置类
│   │   ├── controller/
│   │   │   ├── admin/           # 管理端控制器
│   │   │   └── user/            # 用户端控制器
│   │   ├── interceptor/         # 拦截器
│   │   ├── mapper/              # MyBatis映射
│   │   ├── service/             # 业务逻辑接口
│   │   │   └── impl/            # 业务逻辑实现
│   │   ├── task/                # 定时任务
│   │   ├── websocket/           # WebSocket服务
│   │   └── SkyApplication.java  # 应用入口
│   ├── src/main/resources/
│   │   ├── mapper/              # SQL映射文件
│   │   ├── application.yml      # 应用配置
│   │   └── application-*.yml    # 不同环境配置
└── frontend/                    # 前端项目目录
    ├── admin/                   # 管理端前端
    └── user/                    # 用户端小程序
```

## 🛠 开发环境

* JDK 17
* Maven 3.8+
* MySQL 8.0.30
* Redis 6.0+
* IntelliJ IDEA / Eclipse
* 微信开发者工具
* Node.js 14+
* npm 6+

## 🚀 快速开始

### 环境准备

1. 安装JDK 17，配置JAVA_HOME环境变量
   ```bash
   # 验证JDK安装
   java -version
   ```

2. 安装Maven 3.8+，配置MAVEN_HOME环境变量
   ```bash
   # 验证Maven安装
   mvn -v
   ```

3. 安装并启动MySQL 8.0+
   ```bash
   # 创建数据库
   mysql -u root -p
   CREATE DATABASE sky_take_out DEFAULT CHARACTER SET utf8mb4;
   ```

4. 安装并启动Redis服务
   ```bash
   # 验证Redis安装
   redis-cli ping
   ```

5. 创建数据库并导入初始化SQL脚本
   ```bash
   mysql -u root -p sky_take_out < doc/db/sky_take_out.sql
   ```

6. 配置application-env.yml文件
   ```yaml
   # 创建 application-env.yml 并配置以下内容
   sky:
     datasource:
       driver-class-name: com.mysql.cj.jdbc.Driver
       url: ******************************************************************************************************************************************************************************************
       username: root
       password: 你的密码
       type: com.alibaba.druid.pool.DruidDataSource
     redis:
       host: localhost
       port: 6379
       password: 
       database: 0
     wechat:
       appid: 你的微信小程序appid
       secret: 你的微信小程序secret
       mchid: 你的微信支付商户号
       mchSerialNo: 你的微信支付序列号
       privateKeyFilePath: 你的微信支付私钥文件路径
       apiV3Key: 你的微信支付APIv3密钥
       weChatPayCertFilePath: 你的微信支付证书文件路径
       notifyUrl: 你的微信支付回调通知地址
     oss:
       endpoint: 你的阿里云OSS端点
       bucket-name: 你的阿里云OSS桶名
       access-key-id: 你的阿里云AccessKeyId
       access-key-secret: 你的阿里云AccessKeySecret
   ```

### 后端启动

```bash
# 克隆项目
git clone https://github.com/yourusername/sky-take-out.git

# 进入项目目录
cd sky-take-out

# 编译安装
mvn clean install -DskipTests

# 启动服务
cd sky-server
mvn spring-boot:run
```

服务启动后，访问接口文档：http://localhost:8080/doc.html

### 前端启动

#### 移动端（微信小程序）
1. 使用微信开发者工具打开项目frontend/user目录
2. 修改`app.js`中的`baseUrl`为后端服务地址
3. 配置微信开发者工具中的小程序ID
4. 点击编译运行

#### 管理端
```bash
cd frontend/admin
npm install
npm run serve
```

访问管理端：http://localhost:8081

## 📷 功能演示

### 用户端
![用户登录](https://github.com/yourusername/sky-take-out/raw/master/doc/images/user-login.png)
![菜品浏览](https://github.com/yourusername/sky-take-out/raw/master/doc/images/user-dishes.png)
![订单支付](https://github.com/yourusername/sky-take-out/raw/master/doc/images/user-payment.png)

### 管理端
![工作台](https://github.com/yourusername/sky-take-out/raw/master/doc/images/admin-workspace.png)
![菜品管理](https://github.com/yourusername/sky-take-out/raw/master/doc/images/admin-dish.png)
![数据统计](https://github.com/yourusername/sky-take-out/raw/master/doc/images/admin-stats.png)

## 🔥 项目亮点

### 性能优化
1. **Redis缓存**: 使用Redis缓存热点数据（菜品列表、套餐列表等）
2. **定时任务**: 使用Spring Task定期清理临时数据、生成统计报表
3. **数据库优化**: 对关键表添加索引，优化SQL查询
4. **图片优化**: 采用阿里云OSS存储和CDN加速，提升图片加载速度

### 安全特性
1. **Token认证**: 基于JWT的用户身份验证
2. **数据脱敏**: 敏感信息（如手机号）展示时部分隐藏
3. **接口限流**: 防止恶意请求，保障系统稳定性
4. **权限控制**: 基于角色的权限管理系统

### 业务特色
1. **营业状态控制**: 支持设置营业时间，非营业时间自动拒绝接单
2. **多规格菜品**: 支持同一菜品不同口味、规格和价格
3. **数据统计分析**: 提供多维度的经营数据分析报表
4. **实时通知**: 使用WebSocket实现新订单实时通知

## ❓ 常见问题

### 1. 如何修改端口号？
修改`sky-server/src/main/resources/application.yml`文件中的`server.port`属性。

### 2. 如何添加管理员账号？
系统初始管理员账号为：admin，密码：123456。可通过管理员登录后添加其他员工账号。

### 3. 微信支付配置问题
确保正确配置了微信支付相关参数，包括商户号、API密钥等。详细配置说明请参考[微信支付官方文档](https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml)。

### 4. 图片上传失败
检查阿里云OSS配置是否正确，包括AccessKey、SecretKey、Bucket名称等。

### 5. Redis连接失败
确保Redis服务已启动，并检查配置文件中的连接信息是否正确。

## 📝 开发规范

### 命名规范
- **类名**: 使用大驼峰命名法，如`DishController`
- **方法名**: 使用小驼峰命名法，如`getDishById`
- **变量名**: 使用小驼峰命名法，如`dishName`
- **常量名**: 全大写，单词间用下划线分隔，如`MAX_COUNT`

### 代码格式
- 使用4个空格缩进
- 类、方法加上必要的注释
- 单个方法体不超过80行，单个类不超过600行

### Git提交规范
```
feat: 新功能
fix: 修复Bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 📊 版本更新

### v1.0.0 (2023-09-01)
- 首次发布
- 实现基础点餐功能
- 实现商家管理功能

### v1.1.0 (2023-10-15)
- 新增数据统计功能
- 优化用户体验
- 修复已知问题

## 👨‍💻 开发贡献

1. Fork 本仓库
2. 创建您的功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 📄 许可证

[MIT](LICENSE)