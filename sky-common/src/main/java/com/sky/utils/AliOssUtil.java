package com.sky.utils;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.EnvironmentVariableCredentialsProvider;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.io.ByteArrayInputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;


@Data
@AllArgsConstructor
@Slf4j
public class AliOssUtil {

    private String endpoint;
    private String bucketName;
    private String AccessKeyId;
    private String AccessKeySecret;

    /**
     * 生成一个唯一的 Bucket 名称
     */
    public static String generateUniqueName(String prefix) {
        // 获取当前时间
        LocalDate timestamp = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yy/MM/dd");
        UUID uuid = UUID.randomUUID();
        // 连接以形成一个唯一的 Bucket 名称
        return timestamp.format(formatter) + "-" + uuid.toString().replace("-", "") + prefix;
    }

    /**
     * 文件上传
     *
     * @param content
     * @param objectName
     * @return
     */
    public String upload(byte[] content, String objectName) {

        // 创建 OSSClient 实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, AccessKeyId, AccessKeySecret);

        try{
            // 上传文件
            ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(content));
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message: " + oe.getErrorMessage());
            System.out.println("Error Code:    " + oe.getErrorCode());
            System.out.println("Request ID:    " + oe.getRequestId());
            System.out.println("Host ID:       " + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message: " + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

        //拼接文件访问路径
        //文件访问路径规则 https://<bucketName>.<endpoint>/<objectName>
        return "https://" + bucketName + "." + endpoint + "/" + objectName;
    }
}
